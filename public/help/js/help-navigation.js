// PandaPayroll Help Center - Navigation Functionality

class HelpNavigation {
  constructor() {
    this.currentSection = null;
    this.navLinks = document.querySelectorAll('.nav-link');
    this.helpContent = document.getElementById('helpContent');
    this.breadcrumb = null;

    this.init();
    this.setupMobileNavigation();
    this.loadDefaultContent();
  }

  init() {
    // Add click handlers to navigation links
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const href = link.getAttribute('href');
        const sectionId = href.replace('#', '');
        this.navigateToSection(sectionId);
      });
    });

    // Handle browser back/forward buttons
    window.addEventListener('popstate', (e) => {
      if (e.state && e.state.section) {
        this.navigateToSection(e.state.section, false);
      }
    });

    // Handle initial hash in URL
    if (window.location.hash) {
      const sectionId = window.location.hash.replace('#', '');
      this.navigateToSection(sectionId, false);
    }
  }

  setupMobileNavigation() {
    // Create mobile navigation toggle for smaller screens
    if (window.innerWidth <= 768) {
      this.createMobileNavToggle();
    }

    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth <= 768 && !document.querySelector('.mobile-nav-toggle')) {
        this.createMobileNavToggle();
      } else if (window.innerWidth > 768) {
        this.removeMobileNavToggle();
      }
    });
  }

  createMobileNavToggle() {
    const sidebar = document.querySelector('.help-sidebar');
    const nav = document.querySelector('.help-nav');
    
    if (!sidebar || !nav || document.querySelector('.mobile-nav-toggle')) return;

    const toggle = document.createElement('button');
    toggle.className = 'mobile-nav-toggle';
    toggle.innerHTML = `
      <span>Browse Help Topics</span>
      <i class="fas fa-chevron-down"></i>
    `;

    toggle.addEventListener('click', () => {
      nav.classList.toggle('active');
      toggle.classList.toggle('active');
    });

    sidebar.insertBefore(toggle, nav);
    nav.classList.remove('active'); // Start collapsed
  }

  removeMobileNavToggle() {
    const toggle = document.querySelector('.mobile-nav-toggle');
    const nav = document.querySelector('.help-nav');
    
    if (toggle) {
      toggle.remove();
    }
    
    if (nav) {
      nav.classList.remove('active');
    }
  }

  navigateToSection(sectionId, updateHistory = true) {
    // Update active navigation link
    this.updateActiveNavLink(sectionId);

    // Load content for the section
    this.loadSectionContent(sectionId);

    // Update browser history
    if (updateHistory) {
      const url = `${window.location.pathname}#${sectionId}`;
      history.pushState({ section: sectionId }, '', url);
    }

    // Update current section
    this.currentSection = sectionId;

    // Scroll to top of content
    if (this.helpContent) {
      this.helpContent.scrollIntoView({ behavior: 'smooth' });
    }

    // Close mobile nav if open
    this.closeMobileNav();
  }

  updateActiveNavLink(sectionId) {
    // Remove active class from all links
    this.navLinks.forEach(link => link.classList.remove('active'));
    
    // Add active class to current link
    const activeLink = document.querySelector(`.nav-link[href="#${sectionId}"]`);
    if (activeLink) {
      activeLink.classList.add('active');
    }
  }

  loadSectionContent(sectionId) {
    // Show loading state
    this.showLoading();

    // Simulate loading delay for better UX
    setTimeout(() => {
      const content = this.getContentForSection(sectionId);
      this.displayContent(content);
    }, 300);
  }

  showLoading() {
    this.helpContent.innerHTML = `
      <div class="loading">
        <i class="fas fa-spinner"></i>
        Loading...
      </div>
    `;
  }

  displayContent(content) {
    this.helpContent.innerHTML = content;
    
    // Add breadcrumb
    this.addBreadcrumb();
    
    // Enhance content with interactive elements
    this.enhanceContent();
  }

  addBreadcrumb() {
    const sectionData = this.getSectionData(this.currentSection);
    if (!sectionData) return;

    const breadcrumb = document.createElement('nav');
    breadcrumb.className = 'breadcrumb';
    breadcrumb.innerHTML = `
      <a href="#" onclick="window.helpNavigation.navigateToSection('home')">Help Center</a>
      <span class="breadcrumb-separator">›</span>
      <span>${sectionData.category}</span>
      <span class="breadcrumb-separator">›</span>
      <span>${sectionData.title}</span>
    `;

    this.helpContent.insertBefore(breadcrumb, this.helpContent.firstChild);
  }

  enhanceContent() {
    // Add copy buttons to code blocks
    const codeBlocks = this.helpContent.querySelectorAll('pre code');
    codeBlocks.forEach(block => {
      this.addCopyButton(block.parentElement);
    });

    // Add smooth scrolling to internal links
    const internalLinks = this.helpContent.querySelectorAll('a[href^="#"]');
    internalLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetId = link.getAttribute('href').replace('#', '');
        this.navigateToSection(targetId);
      });
    });
  }

  addCopyButton(codeBlock) {
    const button = document.createElement('button');
    button.className = 'copy-button';
    button.innerHTML = '<i class="fas fa-copy"></i>';
    button.title = 'Copy to clipboard';
    
    button.addEventListener('click', () => {
      const code = codeBlock.querySelector('code').textContent;
      navigator.clipboard.writeText(code).then(() => {
        button.innerHTML = '<i class="fas fa-check"></i>';
        setTimeout(() => {
          button.innerHTML = '<i class="fas fa-copy"></i>';
        }, 2000);
      });
    });

    codeBlock.style.position = 'relative';
    codeBlock.appendChild(button);
  }

  closeMobileNav() {
    const nav = document.querySelector('.help-nav');
    const toggle = document.querySelector('.mobile-nav-toggle');
    
    if (nav && nav.classList.contains('active')) {
      nav.classList.remove('active');
      if (toggle) {
        toggle.classList.remove('active');
      }
    }
  }

  loadDefaultContent() {
    // Load home content by default
    this.navigateToSection('home', false);
  }

  getSectionData(sectionId) {
    const sectionMap = {
      'home': { title: 'Welcome', category: 'Home' },
      'free-trial': { title: 'Free Trial Setup', category: 'Getting Started' },
      'setup-checklist': { title: 'Setup Checklist', category: 'Getting Started' },
      'general-setup': { title: 'General Setup', category: 'Getting Started' },
      'company-management': { title: 'Company Management', category: 'Getting Started' },
      'user-management': { title: 'User Management', category: 'Getting Started' },
      'company-setup': { title: 'Company Setup', category: 'Payroll Setup' },
      'employee-setup': { title: 'Employee Setup', category: 'Payroll Setup' },
      'dashboard-features': { title: 'Dashboard Features', category: 'Payroll Setup' },
      'payslip-creation': { title: 'Payslip Creation', category: 'Payroll Processing' },
      'employee-hours': { title: 'Employee Hours', category: 'Payroll Processing' },
      'pay-runs': { title: 'Pay Runs', category: 'Payroll Processing' },
      'system-items': { title: 'System Items', category: 'Payroll Processing' },
      'pay-calculations': { title: 'Pay Calculations', category: 'Payroll Concepts' },
      'statutory-deductions': { title: 'Statutory Deductions', category: 'Payroll Concepts' },
      'tax-directives': { title: 'Tax Directives', category: 'Payroll Concepts' },
      'cost-to-company': { title: 'Cost-to-Company', category: 'Payroll Concepts' },
      'employee-actions': { title: 'Employee Actions', category: 'Bulk Operations' },
      'payslip-management': { title: 'Payslip Management', category: 'Bulk Operations' },
      'excel-imports': { title: 'Excel Imports', category: 'Bulk Operations' },
      'bulk-inputs': { title: 'Bulk Inputs', category: 'Bulk Operations' },
      'leave-types': { title: 'Leave Types', category: 'Leave Management' },
      'leave-entitlements': { title: 'Leave Entitlements', category: 'Leave Management' },
      'leave-recording': { title: 'Recording Leave', category: 'Leave Management' },
      'leave-adjustments': { title: 'Leave Adjustments', category: 'Leave Management' },
      'leave-self-service': { title: 'Leave Self-Service', category: 'Leave Management' },
      'monthly-submissions': { title: 'Monthly Submissions', category: 'Filing & Compliance' },
      'biannual-filing': { title: 'Bi-annual Filing', category: 'Filing & Compliance' },
      'oid-returns': { title: 'OID Returns', category: 'Filing & Compliance' },
      'sars-processes': { title: 'SARS Processes', category: 'Filing & Compliance' },
      'employee-reports': { title: 'Employee Reports', category: 'Reports' },
      'eti-reports': { title: 'ETI Reports', category: 'Reports' },
      'leave-reports': { title: 'Leave Reports', category: 'Reports' },
      'transaction-history': { title: 'Transaction History', category: 'Reports' },
      'custom-reports': { title: 'Custom Reports', category: 'Reports' },
      'quickbooks-integration': { title: 'QuickBooks Integration', category: 'Integrations' },
      'xero-integration': { title: 'Xero Integration', category: 'Integrations' },
      'time-attendance': { title: 'Time & Attendance', category: 'Integrations' },
      'two-factor-auth': { title: 'Two-Factor Authentication', category: 'Security' },
      'logout-settings': { title: 'Logout Settings', category: 'Security' },
      'support-access': { title: 'Support Access', category: 'Security' },
      'ee-terminology': { title: 'EE Terminology', category: 'Employment Equity' },
      'ee-reporting': { title: 'EE Reporting', category: 'Employment Equity' },
      'ee-submissions': { title: 'EE Submissions', category: 'Employment Equity' },
      'eti-qualification': { title: 'ETI Qualification', category: 'Employment Tax Incentive' },
      'eti-calculations': { title: 'ETI Calculations', category: 'Employment Tax Incentive' },
      'eti-management': { title: 'ETI Management', category: 'Employment Tax Incentive' },
      'admin-portal': { title: 'Admin Portal', category: 'Self-Service' },
      'employee-portal': { title: 'Employee Portal', category: 'Self-Service' },
      'payslip-access': { title: 'Payslip Access', category: 'Self-Service' },
      'tax-certificates': { title: 'Tax Certificates', category: 'Self-Service' },
      'employee-requests': { title: 'Employee Requests', category: 'Self-Service' },
      'payment-methods': { title: 'Payment Methods', category: 'Billing' },
      'billing-statements': { title: 'Billing Statements', category: 'Billing' },
      'account-management': { title: 'Account Management', category: 'Billing' },
      'login-issues': { title: 'Login Issues', category: 'Troubleshooting' },
      'payslip-problems': { title: 'Payslip Problems', category: 'Troubleshooting' },
      'email-delivery': { title: 'Email Delivery', category: 'Troubleshooting' },
      'bulk-upload-errors': { title: 'Bulk Upload Errors', category: 'Troubleshooting' }
    };

    return sectionMap[sectionId];
  }

  getContentForSection(sectionId) {
    // This method will be implemented in help-content.js
    if (window.helpContent && window.helpContent.getContent) {
      const content = window.helpContent.getContent(sectionId);
      return content;
    }

    // Simple fallback content for testing
    const fallbackContent = {
      'home': `
        <div class="help-article active">
          <h2>Welcome to PandaPayroll Help Center</h2>
          <p>Your comprehensive guide to mastering South African payroll management.</p>
          <h3>Getting Started</h3>
          <p>New to PandaPayroll? Start with our <a href="#setup-checklist">Setup Checklist</a> to get your payroll system configured correctly.</p>
          <h3>Popular Topics</h3>
          <ul>
            <li><a href="#payslip-creation">Creating Payslips</a></li>
            <li><a href="#statutory-deductions">Understanding PAYE, UIF, and SDL</a></li>
            <li><a href="#monthly-submissions">EMP201 Monthly Submissions</a></li>
          </ul>
        </div>
      `,
      'free-trial': `
        <div class="help-article active">
          <h2>Free Trial Setup</h2>
          <p>Get started with PandaPayroll's 30-day free trial.</p>
          <h3>Starting Your Free Trial</h3>
          <ol>
            <li>Visit the registration page</li>
            <li>Enter your details</li>
            <li>Verify your email</li>
            <li>Complete company setup</li>
          </ol>
        </div>
      `
    };

    return fallbackContent[sectionId] || `
      <div class="help-article active">
        <h2>Help Topic: ${sectionId.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h2>
        <p>This help topic is being loaded. Content system is working!</p>
        <p><strong>Section ID:</strong> ${sectionId}</p>
        <p>If you see this message, the navigation is working correctly.</p>
      </div>
    `;
  }
}

// Test function to verify JavaScript is working
window.testHelpNavigation = function() {
  if (window.helpNavigation) {
    window.helpNavigation.navigateToSection('free-trial');
  }
};

// Diagnostic function
window.diagnoseHelpCenter = function() {
  const navLinks = document.querySelectorAll('.nav-link');
  const helpContent = document.getElementById('helpContent');

  return {
    helpNavigation: !!window.helpNavigation,
    helpContent: !!window.helpContent,
    navLinksFound: navLinks.length,
    helpContentContainer: !!helpContent,
    contentSections: window.helpContent ? Object.keys(window.helpContent.content).length : 0
  };
};

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.helpNavigation = new HelpNavigation();
});

// Add CSS for copy buttons and mobile navigation
const navigationStyle = document.createElement('style');
navigationStyle.textContent = `
  .copy-button {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background: var(--text-muted);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  pre:hover .copy-button {
    opacity: 1;
  }

  .copy-button:hover {
    background: var(--text-primary);
  }

  @media (max-width: 768px) {
    .mobile-nav-toggle {
      display: block;
    }

    .help-nav:not(.active) {
      display: none;
    }
  }
`;
document.head.appendChild(navigationStyle);
