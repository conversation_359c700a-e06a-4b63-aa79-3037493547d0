<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="<%= csrfToken %>" />
    <title>Employee Leave | <%= company.name %></title>

    <!-- Modern Font -->
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/mobile-employee.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Calendar Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <style>
      /* CSS Variables for Exact employeeProfile Consistency */
      :root {
        --primary-color: #374151;
        --secondary-color: #6b7280;
        --accent-color: #4b5563;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #065f46;
        --warning-color: #92400e;
        --danger-color: #991b1b;
        --info-color: #1e40af;
      }

      /* Optimized Main Content Area Spacing */
      main.main-container {
        flex: 1;
        margin-top: 80px;
        padding: 1.5rem;
        min-height: calc(100vh - 80px);
        background: var(--background-color);
        font-family: 'Inter', sans-serif;
      }

      .layout-wrapper {
        display: flex;
        min-height: 100vh;
      }

      .content-wrapper {
        flex: 1;
        background-color: #f9fafb;
        padding: 0;
      }

      /* Enterprise Profile Header Section - Consistent with employeeProfile */
      .title-section {
        background: var(--card-background);
        border-radius: 4px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: none;
      }

      .profile-header {
        display: flex;
        gap: 1.5rem;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .profile-avatar {
        width: 72px;
        height: 72px;
        border-radius: 4px;
        background: #818cf8;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border: 2px solid var(--border-color);
      }

      .profile-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-initials {
        color: white;
        font-size: 1.25rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        line-height: 1;
      }

      .profile-info {
        flex: 1;
      }

      .profile-name-section {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .profile-name {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin: 0;
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        font-family: 'Inter', sans-serif;
      }

      .employee-status {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #dcfce7;
        color: var(--success-color);
        border: 1px solid #bbf7d0;
        line-height: 1.25;
      }

      .employee-status[data-status="Active"] {
        background: #dcfce7;
        color: var(--success-color);
        border-color: #bbf7d0;
      }

      .employee-status[data-status="Inactive"] {
        background: #fee2e2;
        color: var(--danger-color);
        border-color: #fecaca;
      }

      .employee-status[data-status="Serving Notice"] {
        background: #fef3c7;
        color: var(--warning-color);
        border-color: #fde68a;
      }

      .employee-status i {
        font-size: 0.5rem;
        line-height: 1;
      }

      .profile-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 400;
        text-transform: uppercase;
        letter-spacing: 0.025em;
      }

      .profile-title i {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      /* Quick Info Section - Exact Match with employeeProfile */
      .quick-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--border-color);
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .info-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: var(--background-color);
        border-radius: 2px;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
      }

      .info-icon i {
        font-size: 0.875rem;
      }

      .info-text {
        display: flex;
        flex-direction: column;
        gap: 0.125rem;
      }

      .info-label {
        color: #9ca3af;
        font-size: 0.6875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .info-value {
        color: var(--text-primary);
        font-size: 0.875rem;
        font-weight: 500;
        font-family: 'Inter', monospace;
      }

      /* Enterprise Tab Navigation - Consistent with employeeProfile */
      #tabs-container {
        margin-bottom: 1.5rem;
      }

      #tabs-section {
        display: flex;
        gap: 0;
        align-items: center;
        flex-wrap: wrap;
        padding: 0;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 2px;
      }

      .tab-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: transparent;
        border: none;
        border-right: 1px solid var(--border-color);
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
        transition: background-color 0.1s ease;
        text-decoration: none;
        border-radius: 0;
        line-height: 1.25;
      }

      .tab-button:last-child {
        border-right: none;
      }

      .tab-button i {
        font-size: 0.875rem;
        line-height: 1;
      }

      .tab-button:hover {
        background: var(--border-light);
        color: var(--text-primary);
      }

      .tab-button.active {
        background: #818cf8;
        color: white;
      }

      .tab-button.warning {
        color: var(--warning-color);
      }

      .tab-button.warning:hover {
        background: #fef3c7;
        color: var(--warning-color);
      }

      .tab-button.danger {
        color: var(--danger-color);
      }

      .tab-button.danger:hover {
        background: #fee2e2;
        color: var(--danger-color);
      }

      /* Enterprise Dropdown Styles - Consistent with employeeProfile */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid var(--border-light);
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: var(--border-light);
        color: var(--text-primary);
      }

      .dropdown-content a i {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Enterprise Tag System - Conservative Business Styling */
      .tag {
        font-size: 0.6875rem;
        padding: 0.125rem 0.375rem;
        border-radius: 2px;
        font-weight: 500;
        white-space: nowrap;
        margin-left: 0.5rem;
        border: 1px solid #d1d5db;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        background-color: #f9fafb;
        color: #374151;
      }

      .tag.age-tag {
        background-color: #f9fafb;
        color: #6b7280;
        border-color: #d1d5db;
        font-size: 0.625rem;
        text-transform: none;
        letter-spacing: normal;
      }

      .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
      }

      .header-content h1 {
        font-size: 1.875rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.5rem;
      }

      .header-content .description {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .company-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        background-color: #f3f4f6;
        border-radius: 0.5rem;
        font-size: 0.875rem;
        color: #374151;
      }

      /* Modern Leave Statistics Cards - PandaPayroll Design System */
      .leave-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
      }

      .stat-card {
        background: var(--card-background);
        padding: 1.5rem;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        box-shadow: none;
        transition: all 0.2s ease;
        font-family: 'Inter', sans-serif;
      }

      .stat-card:hover {
        border-color: var(--secondary-color);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .stat-card h3 {
        color: var(--text-secondary);
        font-size: 0.6875rem;
        font-weight: 600;
        margin-bottom: 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .stat-card h3 i {
        color: var(--primary-color);
        font-size: 0.875rem;
      }

      .stat-card .value {
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        font-family: 'Inter', sans-serif;
        margin-bottom: 0.5rem;
      }

      .stat-card .info-text {
        color: var(--text-muted);
        font-size: 0.75rem;
        font-weight: 400;
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid var(--border-light);
      }

      /* Modern Leave Action Buttons - PandaPayroll Design System */
      .leave-actions {
        display: flex;
        gap: 1rem;
        margin-bottom: 2rem;
        flex-wrap: wrap;
      }

      .action-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        font-weight: 500;
        font-size: 0.875rem;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
        transition: all 0.1s ease;
        border: 1px solid transparent;
        line-height: 1.25;
      }

      .action-button i {
        font-size: 0.875rem;
        line-height: 1;
      }

      .primary-button {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
      }

      .primary-button:hover {
        background: var(--accent-color);
        border-color: var(--accent-color);
      }

      .secondary-button {
        background: var(--card-background);
        color: var(--text-secondary);
        border-color: var(--border-color);
      }

      .secondary-button:hover {
        background: var(--border-light);
        color: var(--text-primary);
        border-color: var(--secondary-color);
      }

      /* Modern Leave Calendar - PandaPayroll Design System */
      .leave-calendar {
        background: var(--card-background);
        padding: 1.5rem;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        box-shadow: none;
        margin-bottom: 2rem;
        font-family: 'Inter', sans-serif;
      }

      /* Modern Calendar Styles */
      .fc {
        font-family: "Inter", sans-serif;
        --fc-border-color: #e5e7eb;
        --fc-today-bg-color: #eef2ff;
        --fc-event-bg-color: #4f46e5;
        --fc-event-border-color: #4338ca;
        --fc-event-text-color: #fff;
        --fc-neutral-bg-color: #fff;
      }

      .fc .fc-toolbar {
        margin-bottom: 1.5rem !important;
      }

      .fc .fc-toolbar-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
      }

      .fc .fc-button-group {
        gap: 0.5rem;
      }

      .fc .fc-button {
        background: #fff;
        border: 1px solid #e5e7eb;
        color: #374151;
        font-weight: 500;
        text-transform: capitalize;
        padding: 0.5rem 1rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s;
      }

      .fc .fc-button:hover {
        background: #f9fafb;
        border-color: #d1d5db;
      }

      .fc .fc-button-primary:not(:disabled).fc-button-active,
      .fc .fc-button-primary:not(:disabled):active {
        background: #4f46e5;
        border-color: #4338ca;
        color: #fff;
      }

      .fc .fc-daygrid-day-frame {
        padding: 0.5rem;
      }

      .fc .fc-daygrid-day-top {
        justify-content: center;
        margin-bottom: 0.25rem;
      }

      .fc .fc-daygrid-day-number {
        font-size: 0.875rem;
        color: #374151;
        text-decoration: none;
      }

      .fc .fc-day-today .fc-daygrid-day-number {
        color: #4f46e5;
        font-weight: 600;
      }

      .fc .fc-day-today {
        background: #eef2ff !important;
      }

      .fc .fc-day-other .fc-daygrid-day-number {
        color: #9ca3af;
      }

      .fc .fc-event {
        border-radius: 0.25rem;
        border: none;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
      }

      .fc .fc-event:hover {
        transform: translateY(-1px);
      }

      .fc .fc-event.holiday {
        background-color: #fee2e2 !important;
        border-color: #fecaca !important;
        color: #991b1b !important;
        font-weight: 600;
        border-width: 2px;
        box-shadow: 0 1px 3px rgba(153, 27, 27, 0.2);
      }

      .fc .fc-event.public-holiday {
        background-color: #fee2e2 !important;
        border-color: #fecaca !important;
        color: #991b1b !important;
        font-weight: 600;
        border-width: 2px;
        position: relative;
      }

      .fc .fc-event.public-holiday::before {
        content: '🇿🇦';
        margin-right: 0.25rem;
        font-size: 0.75rem;
      }

      .fc .fc-event.holiday:hover,
      .fc .fc-event.public-holiday:hover {
        background-color: #fecaca !important;
        border-color: #f87171 !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 6px rgba(153, 27, 27, 0.3);
      }

      .fc .fc-event.leave {
        background-color: #dbeafe;
        border-color: #bfdbfe;
        color: #1e40af;
      }

      .fc .fc-event.pending {
        background-color: #fef3c7;
        border-color: #fde68a;
        color: #92400e;
      }

      .fc .fc-daygrid-event-harness {
        margin-bottom: 0.25rem;
      }

      .fc .fc-daygrid-more-link {
        font-size: 0.75rem;
        color: #4f46e5;
        font-weight: 500;
      }

      .fc .fc-popover {
        border: none;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
          0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border-radius: 0.5rem;
      }

      .fc .fc-popover-header {
        background: #f9fafb;
        padding: 0.75rem;
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
        border-bottom: 1px solid #e5e7eb;
      }

      .fc .fc-popover-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #111827;
      }

      .fc .fc-popover-body {
        padding: 0.75rem;
      }

      /* Calendar Filters Styling */
      .calendar-filters {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: var(--background-color);
        border-radius: 4px;
        border: 1px solid var(--border-color);
      }

      .filter-group {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .filter-group label {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
      }

      .checkbox-group {
        display: flex;
        gap: 1rem;
      }

      .checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        font-size: 0.875rem;
        color: var(--text-primary);
      }

      .checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
        border: 1px solid var(--border-color);
        border-radius: 2px;
        cursor: pointer;
      }

      .view-options {
        display: flex;
        gap: 0.5rem;
      }

      .view-btn {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        background: var(--card-background);
        color: var(--text-secondary);
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        transition: all 0.2s ease;
      }

      .view-btn:hover {
        background: var(--border-light);
        color: var(--text-primary);
      }

      .view-btn.active {
        background: #6366f1;
        color: white;
        border-color: #6366f1;
      }

      /* Calendar Legend */
      .calendar-legend {
        display: flex;
        gap: 1.5rem;
        margin-bottom: 1rem;
        padding: 0.75rem 1rem;
        background: var(--card-background);
        border-radius: 4px;
        border: 1px solid var(--border-light);
        font-size: 0.875rem;
      }

      .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
      }

      .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        border: 1px solid rgba(0, 0, 0, 0.1);
      }

      .holiday-color {
        background-color: #fee2e2;
        border-color: #fecaca;
      }

      .leave-color {
        background-color: #dbeafe;
        border-color: #bfdbfe;
      }

      .pending-color {
        background-color: #fef3c7;
        border-color: #fde68a;
      }

      /* Loading Spinner */
      .loading-spinner {
        width: 12px;
        height: 12px;
        border: 2px solid #e5e7eb;
        border-top: 2px solid #6366f1;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @media (max-width: 768px) {
        .content-wrapper {
          padding: 1rem;
        }

        .leave-stats {
          grid-template-columns: 1fr;
        }

        .leave-actions {
          flex-direction: column;
        }

        .action-button {
          width: 100%;
          justify-content: center;
        }

        .calendar-filters {
          flex-direction: column;
          gap: 1rem;
          align-items: stretch;
        }

        .calendar-legend {
          flex-direction: column;
          gap: 0.75rem;
        }

        /* Mobile Calendar Styles */
        .fc {
          font-size: 14px;
        }

        .fc .fc-toolbar {
          flex-direction: column;
          gap: 1rem;
          padding: 0;
        }

        .fc .fc-toolbar-title {
          font-size: 1.125rem;
          text-align: center;
        }

        .fc .fc-header-toolbar {
          margin-bottom: 1.25rem !important;
        }

        .fc .fc-toolbar-chunk {
          display: flex;
          justify-content: center;
          width: 100%;
        }

        .fc .fc-toolbar-chunk:first-child {
          order: 2;
        }

        .fc .fc-toolbar-chunk:nth-child(2) {
          order: 1;
        }

        .fc .fc-toolbar-chunk:last-child {
          order: 3;
        }

        .fc .fc-button {
          padding: 0.375rem 0.75rem;
          font-size: 0.875rem;
        }

        .fc .fc-button-group {
          width: 100%;
          justify-content: center;
        }

        .fc .fc-button-group > .fc-button {
          flex: 1;
        }

        .fc .fc-daygrid-day-number {
          font-size: 0.75rem;
          padding: 0.25rem;
        }

        .fc .fc-daygrid-day-top {
          flex-direction: row;
          justify-content: center;
        }

        .fc .fc-daygrid-day-frame {
          min-height: 4rem;
          padding: 0.25rem;
        }

        .fc .fc-daygrid-event {
          font-size: 0.6875rem;
          padding: 0.125rem 0.25rem;
          margin: 0.125rem 0;
        }

        .fc .fc-daygrid-more-link {
          font-size: 0.6875rem;
        }

        .fc .fc-daygrid-day-events {
          margin: 0;
        }

        .fc .fc-col-header-cell-cushion {
          font-size: 0.75rem;
          padding: 0.25rem;
        }

        .fc .fc-view-harness {
          height: auto !important;
        }

        .fc .fc-scrollgrid {
          border-radius: 0.375rem;
        }

        .fc .fc-scrollgrid-section > td {
          border-radius: 0;
        }

        .fc .fc-popover {
          max-width: calc(100vw - 2rem);
          left: 50% !important;
          transform: translateX(-50%);
        }
      }

      /* Modal Styles */
      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
      }

      .modal-content {
        position: relative;
        background: white;
        margin: 2rem auto;
        padding: 2rem;
        max-width: 600px;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
      }

      .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #64748b;
        cursor: pointer;
      }

      .form-group {
        margin-bottom: 1.5rem;
      }

      .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        color: #475569;
        font-size: 0.875rem;
        font-weight: 500;
      }

      .form-group select,
      .form-group input[type="date"],
      .form-group textarea {
        width: 100%;
        padding: 0.625rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        color: #1e293b;
      }

      .form-group select:focus,
      .form-group input[type="date"]:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: #4f46e5;
        box-shadow: 0 0 0 1px #4f46e5;
      }

      .date-range {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
      }

      .info-text {
        font-size: 0.75rem;
        color: #64748b;
        margin-top: 0.25rem;
      }

      .error-text {
        color: #dc2626;
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }

      .warning-text {
        color: #d97706;
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }

      #documentUpload {
        margin-top: 1rem;
        padding: 1rem;
        border: 1px dashed #e2e8f0;
        border-radius: 0.375rem;
      }

      .accrual-info {
        background: #f8fafc;
        padding: 0.75rem;
        border-radius: 0.375rem;
        margin-top: 0.5rem;
      }

      .accrual-info p {
        font-size: 0.75rem;
        color: #475569;
        margin: 0;
      }

      /* Balance display styles */
      #leaveBalanceDisplay {
        margin-top: 0.5rem;
        padding: 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
      }

      #leaveBalanceDisplay .insufficient {
        color: #dc2626;
        background-color: #fee2e2;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        display: inline-block;
      }

      #leaveBalanceDisplay .sufficient {
        color: #059669;
        background-color: #d1fae5;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        display: inline-block;
      }

      /* History item styles */
      .history-item {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        background-color: #f8fafc;
        border-left: 4px solid #64748b;
      }

      .history-item.approved {
        border-left-color: #059669;
      }

      .history-item.pending {
        border-left-color: #d97706;
      }

      .history-item.rejected {
        border-left-color: #dc2626;
      }

      .history-item.cancelled {
        border-left-color: #64748b;
      }

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .history-title {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .history-header h3 {
        margin: 0;
        font-size: 1rem;
        font-weight: 600;
      }

      .history-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .delete-leave-btn {
        background: none;
        border: none;
        color: #dc2626;
        cursor: pointer;
        padding: 0.25rem;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: 1rem;
      }

      .delete-leave-btn:hover {
        background-color: #fee2e2;
        color: #b91c1c;
      }

      .delete-leave-btn:active {
        transform: scale(0.95);
      }

      .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        text-transform: capitalize;
      }

      .status-badge.approved {
        background-color: #d1fae5;
        color: #059669;
      }

      .status-badge.pending {
        background-color: #fef3c7;
        color: #d97706;
      }

      .status-badge.rejected {
        background-color: #fee2e2;
        color: #dc2626;
      }

      .status-badge.cancelled {
        background-color: #f1f5f9;
        color: #64748b;
      }

      .history-dates {
        font-size: 0.875rem;
        color: #475569;
        margin-bottom: 0.5rem;
      }

      .history-reason {
        font-size: 0.875rem;
        color: #64748b;
        font-style: italic;
      }

      .no-data {
        text-align: center;
        color: #64748b;
        font-style: italic;
        padding: 1rem;
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        margin-top: 2rem;
        padding-top: 1rem;
        border-top: 1px solid #e2e8f0;
      }

      /* Responsive Design - Optimized for Modern Layout */
      @media (max-width: 1024px) {
        main.main-container {
          padding: 1rem;
        }

        .leave-stats {
          grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
          gap: 1rem;
        }

        .quick-info {
          grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
          gap: 1rem;
        }
      }

      @media (max-width: 768px) {
        main.main-container {
          padding: 1rem;
        }

        .title-section {
          padding: 1.5rem;
        }

        .profile-header {
          flex-direction: column;
          text-align: center;
          gap: 1rem;
        }

        .profile-name {
          flex-direction: column;
          gap: 0.5rem;
          font-size: 1.25rem;
        }

        .leave-stats {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .leave-actions {
          flex-direction: column;
          align-items: stretch;
        }

        .action-button {
          justify-content: center;
          min-height: 44px;
        }

        .quick-info {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        #tabs-section {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;
        }

        .tab-button {
          justify-content: center;
          padding: 0.75rem 1rem;
          min-height: 44px;
          border-right: none;
          border-bottom: 1px solid var(--border-color);
        }

        .tab-button:last-child {
          border-bottom: none;
        }

        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid var(--border-color);
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }

      @media (max-width: 480px) {
        .title-section {
          padding: 1rem;
        }

        .profile-avatar {
          width: 60px;
          height: 60px;
        }

        .avatar-initials {
          font-size: 1.25rem;
        }

        .profile-name {
          font-size: 1.125rem;
        }

        .info-icon {
          width: 32px;
          height: 32px;
        }

        .info-icon i {
          font-size: 0.875rem;
          line-height: 1;
        }

        .stat-card {
          padding: 1rem;
        }

        .stat-card h3 {
          font-size: 0.625rem;
        }

        .stat-card .value {
          font-size: 1.25rem;
        }

        .tab-button {
          min-height: 48px;
          padding: 0.875rem 1rem;
        }

        .dropdown {
          width: 100%;
        }

        .dropdown-content {
          width: 100%;
          margin-top: 0.25rem;
        }

        .dropdown-content a {
          padding: 1.25rem 1rem;
          font-size: 1rem;
          border-bottom: 1px solid #f1f5f9;
        }

        .dropdown-content a:last-child {
          border-bottom: none;
        }
      }

      /* Touch-friendly interactions */
      @media (hover: none) and (pointer: coarse) {
        .dropdown-content a {
          padding: 1rem;
          min-height: 48px;
        }

        .tab-button {
          min-height: 48px;
          padding: 0.875rem 1rem;
        }
      }
    </style>
  </head>
  <body data-company-code="<%= company.companyCode %>">
    <div class="layout-wrapper">
      <%- include('partials/sidebar', { activePage: 'employees', company:
      company }) %>
      <div class="content-wrapper">
        <%- include('partials/header', { pageTitle: 'Employee Leave', company:
        company }) %>

        <main class="main-container">
          <!-- Enterprise Profile Header Section -->
          <section class="title-section">
            <div class="profile-header">
              <div class="profile-avatar">
                <% if (employee.profilePicture) { %>
                  <img src="<%= employee.profilePicture %>" alt="<%= employee.firstName %> <%= employee.lastName %>" />
                <% } else { %>
                  <span class="avatar-initials">
                    <%= employee.firstName.charAt(0) %><%= employee.lastName.charAt(0) %>
                  </span>
                <% } %>
              </div>
              <div class="profile-info">
                <div class="profile-name-section">
                  <h1 class="profile-name">
                    <%= employee.firstName %> <%= employee.lastName %>
                    <span class="employee-status" data-status="<%= employee.status %>">
                      <i class="ph ph-circle-fill"></i>
                      <%= employee.status %>
                    </span>
                  </h1>
                  <div class="profile-title">
                    <i class="ph ph-briefcase"></i>
                    <%= employee.jobTitle || 'No Job Title' %>
                  </div>
                </div>
              </div>
            </div>

            <div class="quick-info">
              <div class="info-item" title="Employee ID">
                <div class="info-icon">
                  <i class="ph ph-identification-card"></i>
                </div>
                <div class="info-text">
                  <span class="info-label">Employee ID</span>
                  <span class="info-value"><%= employee.companyEmployeeNumber || 'Not Assigned' %></span>
                </div>
              </div>

              <div class="info-item" title="Pay Frequency">
                <div class="info-icon">
                  <i class="ph ph-calendar"></i>
                </div>
                <div class="info-text">
                  <span class="info-label">Pay Frequency</span>
                  <span class="info-value"><%= payFrequency || 'Not Set' %></span>
                </div>
              </div>

              <div class="info-item" title="Department">
                <div class="info-icon">
                  <i class="ph ph-buildings"></i>
                </div>
                <div class="info-text">
                  <span class="info-label">Department</span>
                  <span class="info-value"><%= employee.department || 'Not Assigned' %></span>
                </div>
              </div>
              <%
                // Calculate employee age
                const calculateAge = (dob) => {
                  const birthDate = new Date(dob);
                  const today = new Date();
                  let age = today.getFullYear() - birthDate.getFullYear();
                  const monthDiff = today.getMonth() - birthDate.getMonth();
                  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                  }
                  return age;
                };

                const employeeAge = employee.dob ? calculateAge(employee.dob) : 'N/A';
              %>
              <% if (dobFormatted) { %>
                  <div class="info-item" title="Date of Birth">
                    <div class="info-icon">
                      <i class="ph ph-cake"></i>
                    </div>
                    <div class="info-text">
                      <span class="info-label">Date of Birth</span>
                      <span class="info-value">
                          <%= dobFormatted %>
                          <% if (employeeAge && employeeAge > 0) { %>
                              <span class="tag age-tag"><%= employeeAge %> years</span>
                          <% } %>
                      </span>
                    </div>
                  </div>
              <% } %>
            </div>
          </section>

          <!-- Professional Action Navigation -->
          <div class="action-tabs">
              <!-- Payroll Tab -->
              <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                <i class="ph ph-calculator"></i>
                Payroll
              </button>

              <!-- Edit Info Dropdown -->
              <div class="dropdown">
                <button class="tab-button" onclick="toggleDropdown(event)">
                  <i class="ph ph-pencil-simple"></i>
                  Edit Info
                  <i class="ph ph-caret-down"></i>
                </button>
                <div id="dropdownContent" class="dropdown-content">
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                    <i class="ph ph-user"></i>
                    Basic Info
                  </a>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                    <i class="ph ph-tag"></i>
                    Classifications
                  </a>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                    <i class="ph ph-file-text"></i>
                    Define RFI
                  </a>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                    <i class="ph ph-clock"></i>
                    Regular Hours
                  </a>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                    <i class="ph ph-chart-line"></i>
                    ETI
                  </a>
                  <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                    <i class="ph ph-star"></i>
                    Skills Equity
                  </a>
                </div>
              </div>

              <!-- Leave Tab (Active) -->
              <a href="/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave"
                 class="tab-button active"
                 role="button">
                <i class="ph ph-calendar-blank"></i>
                Leave
              </a>

              <!-- End Service Tab -->
              <button class="tab-button warning" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service'">
                <i class="ph ph-sign-out"></i>
                <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
              </button>

              <!-- Delete Employee Tab -->
              <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                <i class="ph ph-trash"></i>
                Delete Employee
              </button>
          </div>

          <!-- Enhanced Leave Stats -->
          <div class="leave-stats">
            <% leaveTypes.forEach(function(type) { %>
            <div class="stat-card">
              <h3>
                <i class="ph <%= type.icon %>"></i>
                <%= type.name %>
              </h3>
              <div class="value">
                <%= type.balance %> / <%= type.total %> days
              </div>
              <% if (type.accrualRate === 'monthly') { %>
              <div class="info-text">
                Accrues monthly: <%= type.monthlyAmount %> days
              </div>
              <% } %>
            </div>
            <% }); %>
          </div>

          <div class="leave-actions">
            <button
              class="action-button primary-button"
              onclick="openLeaveModal()"
            >
              <i class="ph ph-plus"></i>
              Request Leave
            </button>
            <button
              class="action-button secondary-button"
              onclick="showLeaveHistory()"
            >
              <i class="ph ph-file-text"></i>
              View History
            </button>
            <% if (user.isManager) { %>
            <button
              class="action-button secondary-button"
              onclick="showTeamCalendar()"
            >
              <i class="ph ph-users"></i>
              Team Calendar
            </button>
            <% } %>
          </div>

          <!-- Enhanced Calendar Section -->
          <div class="calendar-wrapper">
            <div class="calendar-filters">
              <div class="filter-group">
                <label>Show:</label>
                <div class="checkbox-group">
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      checked
                      onchange="toggleEvents('leave')"
                    />
                    <span>Leave</span>
                  </label>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      checked
                      onchange="toggleEvents('pending')"
                    />
                    <span>Pending</span>
                  </label>
                  <label class="checkbox">
                    <input
                      type="checkbox"
                      checked
                      onchange="toggleEvents('holiday')"
                    />
                    <span>Holidays</span>
                  </label>
                </div>
              </div>
              <div class="view-options">
                <button class="view-btn active" data-view="dayGridMonth">
                  Month
                </button>
                <button class="view-btn" data-view="dayGridWeek">Week</button>
                <button class="view-btn" data-view="listMonth">List</button>
              </div>
            </div>

            <!-- Calendar Legend -->
            <div class="calendar-legend">
              <div class="legend-item">
                <span class="legend-color holiday-color"></span>
                <span>🇿🇦 South African Public Holidays</span>
              </div>
              <div class="legend-item">
                <span class="legend-color leave-color"></span>
                <span>Approved Leave</span>
              </div>
              <div class="legend-item">
                <span class="legend-color pending-color"></span>
                <span>Pending Leave</span>
              </div>
              <div class="legend-item" id="holiday-loading-indicator" style="display: none;">
                <span class="loading-spinner"></span>
                <span>Loading holidays...</span>
              </div>
            </div>
            <div class="leave-calendar">
              <div id="calendar"></div>
            </div>
          </div>
        </main>

        <!-- Leave Request Modal -->
        <div id="leaveRequestModal" class="modal">
          <div class="modal-content">
            <div class="modal-header">
              <h2 class="modal-title">Request Leave</h2>
              <button class="close-btn" onclick="closeLeaveModal()">
                &times;
              </button>
            </div>
            <form id="leaveRequestForm" onsubmit="submitLeaveRequest(event)">
              <div class="form-group">
                <label for="leaveType">Leave Type</label>
                <select
                  id="leaveType"
                  name="leaveType"
                  required
                  onchange="handleLeaveTypeChange(this.value)"
                >
                  <option value="">Select a leave type</option>
                  <% leaveTypes.forEach(function(type) { %> <% if (type.gender
                  === 'all' || type.gender === employee.gender) { %>
                  <option
                    value="<%= type._id %>"
                    data-accrual-rate="<%= type.accrualRate %>"
                    data-min-notice="<%= type.minDaysNotice %>"
                    data-requires-document="<%= type.requiresDocument %>"
                    data-document-days="<%= type.documentRequiredAfterDays %>"
                    data-balance="<%= type.balance %>"
                    data-monthly-amount="<%= type.monthlyAmount || 0 %>"
                  >
                    <%= type.name %> (<%= type.balance %> days available)
                  </option>
                  <% } %> <% }); %>
                </select>
                <div class="balance-info info-text"></div>
              </div>

              <div class="form-group date-range">
                <div>
                  <label for="startDate">Start Date</label>
                  <input
                    type="date"
                    id="startDate"
                    name="startDate"
                    required
                    onchange="updateDateRange()"
                  />
                  <div class="min-notice-info info-text"></div>
                </div>
                <div>
                  <label for="endDate">End Date</label>
                  <input
                    type="date"
                    id="endDate"
                    name="endDate"
                    required
                    onchange="updateDateRange()"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="numberOfDays">Number of Days</label>
                <input
                  type="number"
                  id="numberOfDays"
                  name="numberOfDays"
                  readonly
                />
                <div class="days-info info-text"></div>
                <div id="leaveBalanceDisplay" class="info-text"></div>
              </div>

              <div id="documentUpload" style="display: none" class="form-group">
                <label for="supportingDocument">Supporting Document</label>
                <input
                  type="file"
                  id="supportingDocument"
                  name="supportingDocument"
                />
                <div class="document-info info-text"></div>
              </div>

              <div class="form-group">
                <label for="reason">Reason for Leave</label>
                <textarea
                  id="reason"
                  name="reason"
                  rows="3"
                  required
                ></textarea>
              </div>

              <div class="modal-footer">
                <button
                  type="button"
                  class="secondary-button"
                  onclick="closeLeaveModal()"
                >
                  Cancel
                </button>
                <button type="submit" class="primary-button">
                  Submit Request
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Leave History Modal -->
        <div id="leaveHistoryModal" class="modal">
          <div class="modal-content">
            <div class="modal-header">
              <h2>Leave History</h2>
              <button class="close-btn" onclick="closeHistoryModal()">
                &times;
              </button>
            </div>
            <div class="leave-history">
              <div class="filters">
                <select id="yearFilter">
                  <option value="2024">2024</option>
                  <option value="2023">2023</option>
                </select>
                <select id="statusFilter">
                  <option value="all">All Status</option>
                  <option value="approved">Approved</option>
                  <option value="pending">Pending</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
              <div class="history-list">
                <!-- Will be populated dynamically -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <%- include('partials/mobile-bottom-nav', { req: { path:
    `/clients/${company.companyCode}/employeeManagement` }, company: company })
    %>

    <!-- Enhanced Calendar Script -->
    <script>
      document.addEventListener("DOMContentLoaded", async function () {
        var calendarEl = document.getElementById("calendar");
        var calendar = new FullCalendar.Calendar(calendarEl, {
          initialView: window.innerWidth < 768 ? "dayGridWeek" : "dayGridMonth",
          headerToolbar: {
            left: "prev,next today",
            center: "title",
            right: "dayGridMonth,dayGridWeek",
          },
          height: "auto",
          handleWindowResize: true,
          dayMaxEvents: window.innerWidth < 768 ? 2 : 3,
          views: {
            dayGridMonth: {
              dayMaxEventRows: window.innerWidth < 768 ? 2 : 3,
            },
            dayGridWeek: {
              dayMaxEventRows: 4,
            },
          },
          eventClassNames: function (arg) {
            // Add custom classes based on event type
            if (arg.event.extendedProps.type === "holiday") {
              return ["holiday"];
            } else if (arg.event.extendedProps.status === "pending") {
              return ["pending"];
            } else {
              return ["leave"];
            }
          },
          eventDidMount: function (info) {
            // Add tooltips to events
            info.el.title =
              info.event.title +
              "\n" +
              (info.event.extendedProps.description || "") +
              "\n" +
              moment(info.event.start).format("MMM D, YYYY");
          },
          eventClick: function (info) {
            // Handle event clicks
            console.log("Event clicked:", info.event);
          },
          dateClick: function (info) {
            // Handle date clicks
            console.log("Date clicked:", info.dateStr);
          },
          datesSet: async function(info) {
            // This event fires when the calendar view changes (navigation, view change)
            console.log("Calendar view changed:", info);

            const viewStart = info.start;
            const viewEnd = info.end;

            // Load holidays for the new date range (show loading for user-initiated navigation)
            const holidayData = await loadHolidays(viewStart, viewEnd, true);

            // Remove existing holiday events that are outside the new range
            const existingEvents = calendar.getEvents();
            existingEvents.forEach(event => {
              if (event.extendedProps.type === "holiday") {
                const eventDate = new Date(event.start);
                if (eventDate < viewStart || eventDate > viewEnd) {
                  // Keep events that might still be visible, only remove if clearly outside range
                  const bufferDays = 60; // 2 month buffer
                  const bufferStart = new Date(viewStart.getTime() - (bufferDays * 24 * 60 * 60 * 1000));
                  const bufferEnd = new Date(viewEnd.getTime() + (bufferDays * 24 * 60 * 60 * 1000));

                  if (eventDate < bufferStart || eventDate > bufferEnd) {
                    event.remove();
                  }
                }
              }
            });

            // Add new holidays for the current view
            addHolidaysToCalendar(holidayData);
          },
        });

        calendar.render();

        // Load leave requests
        try {
          const leaveResponse = await fetch(
            "/api/leave/requests/<%= employee._id %>",
            {
              headers: {
                "X-CSRF-Token": document.querySelector(
                  'meta[name="csrf-token"]'
                ).content,
              },
            }
          );

          if (!leaveResponse.ok) {
            throw new Error(`HTTP error! status: ${leaveResponse.status}`);
          }

          const leaveData = await leaveResponse.json();
          if (leaveData.success && leaveData.data) {
            leaveData.data.forEach((leave) => {
              calendar.addEvent({
                title: `${leave.leaveType.name}`,
                start: leave.startDate,
                end: leave.endDate,
                extendedProps: {
                  type: "leave",
                  status: leave.status,
                  description: leave.reason,
                },
              });
            });
          }
        } catch (error) {
          console.error("Error loading leave requests:", error);
        }

        // Holiday cache to avoid repeated API calls
        const holidayCache = new Map();

        // Function to load holidays for a specific date range
        async function loadHolidays(startDate, endDate, showLoading = false) {
          const startYear = startDate.getFullYear();
          const endYear = endDate.getFullYear();
          const cacheKey = `${startYear}-${endYear}`;

          // Check cache first
          if (holidayCache.has(cacheKey)) {
            console.log(`Using cached holidays for ${cacheKey}`);
            return holidayCache.get(cacheKey);
          }

          // Show loading indicator if requested
          const loadingIndicator = document.getElementById('holiday-loading-indicator');
          if (showLoading && loadingIndicator) {
            loadingIndicator.style.display = 'flex';
          }

          try {
            console.log(`Fetching South African public holidays for ${startYear}-${endYear}...`);
            const holidaysResponse = await fetch(
              `/api/leave/holidays?startYear=${startYear}&endYear=${endYear}`,
              {
                headers: {
                  "X-CSRF-Token": document.querySelector(
                    'meta[name="csrf-token"]'
                  ).content,
                },
              }
            );

            if (!holidaysResponse.ok) {
              throw new Error(`HTTP error! status: ${holidaysResponse.status}`);
            }

            const holidays = await holidaysResponse.json();
            console.log("Holidays response:", holidays);

            if (holidays.success && holidays.data) {
              // Cache the results
              holidayCache.set(cacheKey, holidays.data);
              console.log(`Cached ${holidays.data.length} holidays for ${cacheKey} (${holidays.meta?.publicHolidayCount || 0} public, ${holidays.meta?.companyHolidayCount || 0} company)`);
              return holidays.data;
            } else {
              console.warn("No holiday data received or invalid response format");
              return [];
            }
          } catch (error) {
            console.error("Error loading holidays:", error);
            console.warn("Unable to load public holidays. Please refresh the page or contact support if the issue persists.");
            return [];
          } finally {
            // Hide loading indicator
            if (showLoading && loadingIndicator) {
              loadingIndicator.style.display = 'none';
            }
          }
        }

        // Function to add holidays to calendar
        function addHolidaysToCalendar(holidayData) {
          holidayData.forEach((holiday) => {
            const holidayEvent = {
              id: `holiday-${holiday.start || holiday.date}`, // Unique ID to prevent duplicates
              title: holiday.title || holiday.name,
              start: holiday.start || holiday.date,
              allDay: true,
              backgroundColor: '#fee2e2',
              borderColor: '#fecaca',
              textColor: '#991b1b',
              className: 'holiday public-holiday',
              extendedProps: {
                type: "holiday",
                isPublicHoliday: holiday.extendedProps?.isPublicHoliday !== false,
                isCompanyHoliday: holiday.extendedProps?.isCompanyHoliday || false,
                description: holiday.extendedProps?.description ||
                           `South African Public Holiday: ${holiday.title || holiday.name}`,
              },
            };

            // Check if event already exists to prevent duplicates
            const existingEvent = calendar.getEventById(holidayEvent.id);
            if (!existingEvent) {
              console.log("Adding holiday event:", holidayEvent);
              calendar.addEvent(holidayEvent);
            }
          });
        }

        // Initial holiday load for current view
        const currentDate = new Date();
        const initialStartDate = new Date(currentDate.getFullYear() - 1, 0, 1); // Start from previous year
        const initialEndDate = new Date(currentDate.getFullYear() + 1, 11, 31); // End at next year

        const initialHolidays = await loadHolidays(initialStartDate, initialEndDate);
        addHolidaysToCalendar(initialHolidays);
        console.log("Successfully loaded initial holidays to calendar");

        // Preload holidays for extended range to improve navigation performance
        setTimeout(async () => {
          try {
            const extendedStartDate = new Date(currentDate.getFullYear() - 2, 0, 1);
            const extendedEndDate = new Date(currentDate.getFullYear() + 2, 11, 31);
            console.log("Preloading extended holiday range...");
            await loadHolidays(extendedStartDate, extendedEndDate);
            console.log("Extended holiday range preloaded successfully");
          } catch (error) {
            console.warn("Failed to preload extended holiday range:", error);
          }
        }, 2000); // Preload after 2 seconds to not block initial load

        // Handle window resize
        window.addEventListener("resize", function () {
          // Update calendar view based on screen width
          if (window.innerWidth < 768) {
            calendar.setOption("dayMaxEvents", 2);
            calendar.setOption("dayMaxEventRows", 2);
          } else {
            calendar.setOption("dayMaxEvents", 3);
            calendar.setOption("dayMaxEventRows", 3);
          }
          calendar.updateSize();
        });

        // Handle orientation change
        window.addEventListener("orientationchange", function () {
          setTimeout(() => {
            calendar.updateSize();
            // Update view based on new orientation
            if (window.innerWidth < 768) {
              calendar.setOption("dayMaxEvents", 2);
              calendar.setOption("dayMaxEventRows", 2);
            } else {
              calendar.setOption("dayMaxEvents", 3);
              calendar.setOption("dayMaxEventRows", 3);
            }
          }, 200);
        });

        // Add event handlers for modals
        window.openLeaveModal = function () {
          document.getElementById("leaveRequestModal").style.display = "block";
        };

        window.closeLeaveModal = function () {
          document.getElementById("leaveRequestModal").style.display = "none";
        };

        async function submitLeaveRequest(event) {
          event.preventDefault();

          try {
            const csrfToken = document.querySelector(
              'meta[name="csrf-token"]'
            ).content;

            // Get form data
            const formData = {
              leaveType: document.getElementById("leaveType").value,
              startDate: document.getElementById("startDate").value,
              endDate: document.getElementById("endDate").value,
              numberOfDays: parseFloat(
                document.getElementById("numberOfDays").value
              ),
              reason: document.getElementById("reason").value,
              employee: "<%= employee._id %>", // Add the employee ID
            };

            console.log("Submitting leave request:", formData);
            console.log("CSRF Token:", csrfToken);

            // Send the request
            const response = await fetch("/api/leave/request", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                "X-CSRF-Token": csrfToken,
              },
              body: JSON.stringify(formData),
            });

            console.log("Response status:", response.status);

            const data = await response.json();
            console.log("Response data:", data);

            if (data.success) {
              // Add event to calendar
              calendar.addEvent({
                title: "Leave Request (Pending)",
                start: formData.startDate,
                end: formData.endDate,
                extendedProps: {
                  type: "leave",
                  status: "pending",
                  description: formData.reason,
                },
              });

              // Close modal and show success message
              closeLeaveModal();
              showNotification(
                "Leave request submitted successfully",
                "success"
              );

              // Refresh page after a short delay
              setTimeout(() => {
                window.location.reload();
              }, 1500);
            } else {
              showNotification(
                data.message || "Failed to submit leave request",
                "error"
              );
            }
          } catch (error) {
            console.error("Error submitting leave request:", error);
            showNotification(
              "An error occurred while submitting the request",
              "error"
            );
          }
        }

        // View switching
        document.querySelectorAll(".view-btn").forEach((btn) => {
          btn.addEventListener("click", function () {
            document
              .querySelectorAll(".view-btn")
              .forEach((b) => b.classList.remove("active"));
            this.classList.add("active");
            calendar.changeView(this.dataset.view);
          });
        });

        // Event filtering
        window.toggleEvents = function (type) {
          const events = calendar.getEvents();
          events.forEach((event) => {
            if (event.extendedProps.type === type) {
              event.setProp(
                "display",
                event.display === "none" ? "auto" : "none"
              );
            }
          });
        };

        // Notification helper
        window.showNotification = function (message, type = "info") {
          const notification = document.createElement("div");
          notification.className = `notification ${type}`;
          notification.textContent = message;
          document.body.appendChild(notification);

          // Style the notification
          Object.assign(notification.style, {
            position: "fixed",
            top: "20px",
            right: "20px",
            padding: "10px 20px",
            borderRadius: "5px",
            zIndex: "9999",
            opacity: "0",
            transition: "opacity 0.3s ease",
          });

          // Set color based on type
          if (type === "success") {
            notification.style.backgroundColor = "#4CAF50";
            notification.style.color = "white";
          } else if (type === "error") {
            notification.style.backgroundColor = "#F44336";
            notification.style.color = "white";
          } else {
            notification.style.backgroundColor = "#2196F3";
            notification.style.color = "white";
          }

          // Show and then hide after 3 seconds
          setTimeout(() => {
            notification.style.opacity = "1";
          }, 10);
          setTimeout(() => {
            notification.style.opacity = "0";
            setTimeout(() => {
              document.body.removeChild(notification);
            }, 300);
          }, 3000);
        };

        // Modal functions
        function handleLeaveTypeChange(leaveTypeId) {
          if (!leaveTypeId) return;

          const option = document.querySelector(
            `option[value="${leaveTypeId}"]`
          );
          const accrualRate = option.dataset.accrualRate;
          const minNotice = parseInt(option.dataset.minNotice);
          const requiresDocument = option.dataset.requiresDocument === "true";
          const documentDays = parseInt(option.dataset.documentDays);
          const balance = parseFloat(option.dataset.balance);
          const monthlyAmount = parseFloat(option.dataset.monthlyAmount);

          // Update minimum date based on notice period
          const startDateInput = document.getElementById("startDate");
          const minDate = new Date();
          minDate.setDate(minDate.getDate() + minNotice);
          startDateInput.min = minDate.toISOString().split("T")[0];

          // Show minimum notice info
          const minNoticeInfo = document.querySelector(".min-notice-info");
          if (minNotice > 0) {
            minNoticeInfo.textContent = `${minNotice} days notice required`;
          } else {
            minNoticeInfo.textContent = "";
          }

          // Show/hide document upload
          const documentUpload = document.getElementById("documentUpload");
          documentUpload.style.display = requiresDocument ? "block" : "none";

          // Update document info text
          if (requiresDocument) {
            document.querySelector(
              ".document-info"
            ).textContent = `Document required for leaves longer than ${documentDays} days`;
          }

          // Update balance info
          const balanceInfo = document.querySelector(".balance-info");
          if (accrualRate === "monthly") {
            balanceInfo.textContent = `Monthly accrual: ${monthlyAmount} days per month. Current balance: ${balance} days`;
          } else {
            balanceInfo.textContent = `Available balance: ${balance} days`;
          }

          // Reset date inputs
          document.getElementById("startDate").value = "";
          document.getElementById("endDate").value = "";
          document.getElementById("numberOfDays").value = "";
        }

        // Update the updateDateRange function to include document requirement check
        function updateDateRange() {
          const startDateInput = document.getElementById("startDate");
          const endDateInput = document.getElementById("endDate");
          const numberOfDaysInput = document.getElementById("numberOfDays");

          if (startDateInput.value && endDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const endDate = new Date(endDateInput.value);

            // Ensure end date is not before start date
            if (endDate < startDate) {
              endDateInput.value = startDateInput.value;
              numberOfDaysInput.value = 1;
              return;
            }

            // Calculate business days between dates
            const businessDays = calculateBusinessDays(startDate, endDate);
            numberOfDaysInput.value = businessDays;

            // Update days info
            const daysInfo = document.querySelector(".days-info");
            daysInfo.textContent = `${businessDays} business days selected`;

            // Check if document is required
            const leaveType = document.getElementById("leaveType");
            if (leaveType.value) {
              const option = leaveType.options[leaveType.selectedIndex];
              const requiresDocument =
                option.dataset.requiresDocument === "true";
              const documentDays = parseInt(option.dataset.documentDays);

              if (requiresDocument && businessDays > documentDays) {
                document.querySelector(
                  ".document-info"
                ).textContent = `Supporting document required for leaves longer than ${documentDays} days`;
                document.getElementById("documentUpload").style.display =
                  "block";
              }
            }

            // Update leave balance display
            updateLeaveBalanceDisplay();
          }
        }

        // Calculate business days between two dates
        function calculateBusinessDays(startDate, endDate) {
          let count = 0;
          const curDate = new Date(startDate.getTime());

          // Adjust to handle same-day requests
          if (
            startDate.getTime() === endDate.getTime() &&
            !isWeekend(startDate)
          ) {
            return 1;
          }

          while (curDate <= endDate) {
            const dayOfWeek = curDate.getDay();
            if (dayOfWeek !== 0 && dayOfWeek !== 6) {
              count++;
            }
            curDate.setDate(curDate.getDate() + 1);
          }

          return count;
        }

        // Check if a date is a weekend
        function isWeekend(date) {
          const dayOfWeek = date.getDay();
          return dayOfWeek === 0 || dayOfWeek === 6;
        }

        // Update leave balance display based on selected leave type and days
        function updateLeaveBalanceDisplay() {
          const leaveTypeSelect = document.getElementById("leaveType");
          const numberOfDaysInput = document.getElementById("numberOfDays");
          const balanceDisplay = document.getElementById("leaveBalanceDisplay");

          if (leaveTypeSelect.value && numberOfDaysInput.value) {
            const selectedOption =
              leaveTypeSelect.options[leaveTypeSelect.selectedIndex];
            const availableBalance = parseFloat(selectedOption.dataset.balance);
            const requestedDays = parseFloat(numberOfDaysInput.value);

            if (availableBalance < requestedDays) {
              balanceDisplay.innerHTML = `<span class="insufficient">Insufficient balance: ${availableBalance} days available</span>`;
            } else {
              balanceDisplay.innerHTML = `<span class="sufficient">Available balance: ${availableBalance} days (${
                availableBalance - requestedDays
              } days remaining after this request)</span>`;
            }
          }
        }

        // Fix the initialization of date inputs and event handlers
        function initializeDateInputs() {
          const today = new Date();
          const startDateInput = document.getElementById("startDate");
          const endDateInput = document.getElementById("endDate");

          // Set minimum date to today
          const todayStr = today.toISOString().split("T")[0];
          startDateInput.min = todayStr;
          endDateInput.min = todayStr;

          // Add event listeners
          startDateInput.addEventListener("change", updateDateRange);
          endDateInput.addEventListener("change", updateDateRange);
        }

        // Ensure the modal functions are properly defined
        function openLeaveModal() {
          document.getElementById("leaveRequestModal").style.display = "block";
          document.getElementById("leaveRequestForm").reset();
          initializeDateInputs();
        }

        function closeLeaveModal() {
          document.getElementById("leaveRequestModal").style.display = "none";
        }

        function showLeaveHistory() {
          // Fetch leave history data
          fetch(`/api/leave/requests/<%= employee._id %>`, {
            headers: {
              "CSRF-Token": document.querySelector('meta[name="csrf-token"]')
                .content,
            },
          })
            .then((response) => response.json())
            .then((data) => {
              if (data.success) {
                // Populate history list
                const historyList = document.querySelector(".history-list");
                historyList.innerHTML = "";

                if (data.data.length === 0) {
                  historyList.innerHTML =
                    '<p class="no-data">No leave requests found</p>';
                } else {
                  data.data.forEach((request) => {
                    const item = document.createElement("div");
                    item.className = `history-item ${request.status}`;

                    // Check if delete button should be shown (only for pending or cancelled requests)
                    const canDelete = ["pending", "cancelled"].includes(request.status);
                    const deleteButtonHtml = canDelete ? `
                      <button class="delete-leave-btn"
                              onclick="confirmDeleteLeaveRequest('${request._id}', '${request.leaveType.name}')"
                              title="Delete leave request">
                        <i class="ph ph-trash"></i>
                      </button>
                    ` : '';

                    item.innerHTML = `
                    <div class="history-header">
                      <div class="history-title">
                        <h3>${request.leaveType.name}</h3>
                        <span class="status-badge ${request.status}">${
                      request.status
                    }</span>
                      </div>
                      <div class="history-actions">
                        ${deleteButtonHtml}
                      </div>
                    </div>
                    <div class="history-dates">
                      ${new Date(
                        request.startDate
                      ).toLocaleDateString()} - ${new Date(
                      request.endDate
                    ).toLocaleDateString()}
                      (${request.numberOfDays} days)
                    </div>
                    <div class="history-reason">${request.reason}</div>
                  `;
                    historyList.appendChild(item);
                  });
                }

                // Show modal
                document.getElementById("leaveHistoryModal").style.display =
                  "block";
              } else {
                showNotification("Failed to load leave history", "error");
              }
            })
            .catch((error) => {
              console.error("Error:", error);
              showNotification("Failed to load leave history", "error");
            });
        }

        function closeHistoryModal() {
          document.getElementById("leaveHistoryModal").style.display = "none";
        }

        // Initialize date inputs for the leave request form
        initializeDateInputs();

        // Leave deletion confirmation modal
        function confirmDeleteLeaveRequest(leaveRequestId, leaveTypeName) {
          // Create modal container
          const modal = document.createElement('div');
          modal.className = 'confirmation-modal';
          modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1050;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          `;

          modal.innerHTML = `
            <div class="modal-content" style="
              background-color: white;
              padding: 2rem;
              border-radius: 8px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              width: 90%;
              max-width: 500px;
              text-align: center;
              position: relative;
              transform: translateY(20px);
              transition: transform 0.3s ease;
              max-height: 90vh;
              overflow-y: auto;
              margin: 0 1rem;
            ">
              <i class="ph ph-warning-circle" style="
                font-size: 3rem;
                color: #f59e0b;
                margin-bottom: 1rem;
                display: inline-block;
              "></i>
              <h3 style="margin-bottom: 1rem; font-size: 1.5rem; color: #111827; font-weight: 600;">Delete Leave Request</h3>
              <p style="margin-bottom: 2rem; color: #4b5563; line-height: 1.5;">
                Are you sure you want to delete the <strong>${leaveTypeName}</strong> leave request? This action cannot be undone.
              </p>
              <div style="display: flex; gap: 1rem; justify-content: center;">
                <button id="cancelDeleteBtn" style="
                  background-color: #f3f4f6;
                  color: #374151;
                  border: 1px solid #d1d5db;
                  padding: 0.75rem 1.5rem;
                  border-radius: 6px;
                  cursor: pointer;
                  font-weight: 500;
                  transition: all 0.2s ease;
                  font-family: inherit;
                  font-size: 0.875rem;
                ">Cancel</button>
                <button id="confirmDeleteBtn" style="
                  background-color: #ef4444;
                  color: white;
                  border: 1px solid #ef4444;
                  padding: 0.75rem 1.5rem;
                  border-radius: 6px;
                  cursor: pointer;
                  font-weight: 500;
                  transition: all 0.2s ease;
                  font-family: inherit;
                  font-size: 0.875rem;
                ">Delete</button>
              </div>
            </div>
          `;

          document.body.appendChild(modal);

          // Prevent body scrolling
          const originalOverflow = document.body.style.overflow;
          document.body.style.overflow = 'hidden';

          // Trigger the animation
          setTimeout(() => {
            modal.style.opacity = '1';
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.transform = 'translateY(0)';
            }
          }, 10);

          // Close modal function
          function closeModal() {
            modal.style.opacity = '0';
            const modalContent = modal.querySelector('.modal-content');
            if (modalContent) {
              modalContent.style.transform = 'translateY(20px)';
            }
            setTimeout(() => {
              document.body.removeChild(modal);
              document.body.style.overflow = originalOverflow;
            }, 300);
          }

          // Handle backdrop click
          modal.addEventListener('click', function(e) {
            if (e.target === modal) {
              closeModal();
            }
          });

          // Handle escape key
          const handleEscape = function(e) {
            if (e.key === 'Escape') {
              closeModal();
              document.removeEventListener('keydown', handleEscape);
            }
          };
          document.addEventListener('keydown', handleEscape);

          // Handle cancel button
          const cancelBtn = document.getElementById('cancelDeleteBtn');
          if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
              closeModal();
              document.removeEventListener('keydown', handleEscape);
            });
          }

          // Handle confirmation
          const confirmBtn = document.getElementById('confirmDeleteBtn');
          if (confirmBtn) {
            confirmBtn.addEventListener('click', async function() {
              // Close modal first
              closeModal();
              document.removeEventListener('keydown', handleEscape);

              // Get CSRF token
              const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

              try {
                const response = await fetch(`/api/leave/request/${leaveRequestId}`, {
                  method: 'DELETE',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': csrfToken
                  }
                });

                const data = await response.json();

                if (response.ok) {
                  showNotification('Leave request deleted successfully', 'success');
                  // Refresh the leave history modal
                  showLeaveHistory();
                  // Refresh the calendar
                  window.location.reload();
                } else {
                  showNotification(data.message || 'Error deleting leave request', 'error');
                }
              } catch (error) {
                console.error('Error:', error);
                showNotification('Connection timeout or server error. Please try again.', 'error');
              }
            });
          }

          // Add button hover effects
          const handleMouseOver = function(e) {
            if (e.target.id === 'cancelDeleteBtn') {
              e.target.style.backgroundColor = '#d1d5db';
            }
            if (e.target.id === 'confirmDeleteBtn') {
              e.target.style.backgroundColor = '#dc2626';
            }
          };

          const handleMouseOut = function(e) {
            if (e.target.id === 'cancelDeleteBtn') {
              e.target.style.backgroundColor = '#f3f4f6';
            }
            if (e.target.id === 'confirmDeleteBtn') {
              e.target.style.backgroundColor = '#ef4444';
            }
          };

          modal.addEventListener('mouseover', handleMouseOver);
          modal.addEventListener('mouseout', handleMouseOut);
        }

        // Make global functions available
        window.openLeaveModal = openLeaveModal;
        window.closeLeaveModal = closeLeaveModal;
        window.showLeaveHistory = showLeaveHistory;
        window.closeHistoryModal = closeHistoryModal;
        window.submitLeaveRequest = submitLeaveRequest;
        window.handleLeaveTypeChange = handleLeaveTypeChange;
        window.updateDateRange = updateDateRange;
        window.confirmDeleteLeaveRequest = confirmDeleteLeaveRequest;

        // Function to fetch employee leave requests
        async function fetchEmployeeLeaveRequests() {
          try {
            const employeeId = '<%= employee._id %>'; // Get employee ID from EJS template
            const response = await fetch(`/api/leave/requests/${employeeId}`, {
              headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
              }
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const { data: leaveRequests } = await response.json();
            
            if (!Array.isArray(leaveRequests)) {
              console.error('Leave requests is not an array:', leaveRequests);
              return;
            }

            // Map leave requests to calendar events
            const leaveRequestEvents = leaveRequests.map(request => ({
              title: `${request.leaveType.name}`,
              start: request.startDate,
              end: request.endDate,
              type: request.leaveType.category ? request.leaveType.category.toLowerCase() : 'other',
              status: request.status
            }));

            // Add leave request events to the leaveEvents array
            leaveEvents = [...leaveEvents, ...leaveRequestEvents];
          } catch (error) {
            console.error("Error fetching leave requests:", error);
          }
        }

        // Function to fetch and update all events
        async function fetchAndUpdateEvents() {
          try {
            // Reset leaveEvents array before fetching new data
            leaveEvents = [];
            
            // Fetch holidays
            await fetchAndAddHolidays();
            
            // Fetch employee's leave requests
            await fetchEmployeeLeaveRequests();
            
            // Update the calendar with all events
            updateLeaveEvents();
          } catch (error) {
            console.error('Error updating events:', error);
          }
        }

        // Tab Navigation Functions
        function toggleDropdown(event) {
          event.stopPropagation();
          const dropdown = event.target.closest('.dropdown');
          const content = dropdown.querySelector('.dropdown-content');
          content.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
          const dropdowns = document.querySelectorAll('.dropdown-content');
          dropdowns.forEach(dropdown => {
            if (!dropdown.closest('.dropdown').contains(event.target)) {
              dropdown.classList.remove('show');
            }
          });
        });

        // Delete Employee Function - using the same implementation as employeeProfile.ejs
        // This function is now loaded from employeeActions.js

        // Make functions globally available
        window.toggleDropdown = toggleDropdown;
      });
    </script>

    <!-- Delete Employee Handler - Must be outside DOMContentLoaded for immediate availability -->
    <script>
      // Wrapper function to safely handle delete employee action
      function handleDeleteEmployee(employeeId) {
        console.log('handleDeleteEmployee called with ID:', employeeId);

        // Validate employee ID
        if (!employeeId) {
          console.error('No employee ID provided to handleDeleteEmployee');
          alert('Error: No employee ID provided. Please refresh the page and try again.');
          return;
        }

        console.log('confirmDeleteEmployee available:', typeof window.confirmDeleteEmployee);

        if (typeof window.confirmDeleteEmployee === 'function') {
          try {
            window.confirmDeleteEmployee(employeeId);
          } catch (error) {
            console.error('Error calling confirmDeleteEmployee:', error);
            alert('Error occurred while trying to delete employee. Please refresh the page and try again.');
          }
        } else {
          console.error('confirmDeleteEmployee function not available. Attempting to wait for script load...');

          // Try to wait for the function to become available
          let attempts = 0;
          const maxAttempts = 15; // Increased attempts
          const checkInterval = setInterval(() => {
            attempts++;
            console.log(`Attempt ${attempts}: Checking for confirmDeleteEmployee...`);

            if (typeof window.confirmDeleteEmployee === 'function') {
              clearInterval(checkInterval);
              console.log('confirmDeleteEmployee function now available, calling it...');
              try {
                window.confirmDeleteEmployee(employeeId);
              } catch (error) {
                console.error('Error calling confirmDeleteEmployee after wait:', error);
                alert('Error occurred while trying to delete employee. Please refresh the page and try again.');
              }
            } else if (attempts >= maxAttempts) {
              clearInterval(checkInterval);
              console.error('confirmDeleteEmployee function still not available after waiting.');
              alert('Delete function not available. Please refresh the page and try again.');
            }
          }, 150); // Slightly longer interval
        }
      }

      // Make function globally available immediately
      window.handleDeleteEmployee = handleDeleteEmployee;
      console.log('handleDeleteEmployee function defined and made globally available');
    </script>

    <!-- Employee Actions Script for consistent modal functionality - Load early to avoid conflicts -->
    <script src="/js/employeeActions.js" onload="console.log('employeeActions.js loaded successfully')" onerror="console.error('Failed to load employeeActions.js')"></script>

    <!-- Scripts -->
    <script src="/table.js"></script>
    <script src="/validate.js"></script>
    <script src="/responsive-script.js"></script>
    <script src="/js/employeeManagement.js"></script>
    <script src="/include.js"></script>
    <script src="/script.js"></script>
    <script src="/main.js"></script>
    <script src="/tab.js"></script>

    <!-- Verify script loading and ensure function availability -->
    <script>
      // Comprehensive verification that the script loaded and function is available
      function verifyEmployeeActionsLoaded() {
        console.log('Verifying employeeActions.js loading...');
        console.log('confirmDeleteEmployee available:', typeof window.confirmDeleteEmployee);

        if (typeof window.confirmDeleteEmployee === 'function') {
          console.log('✅ employeeActions.js loaded successfully and confirmDeleteEmployee is available');
          return true;
        } else {
          console.error('❌ CRITICAL: employeeActions.js did not load properly or confirmDeleteEmployee is not exposed');
          return false;
        }
      }

      // Wait for DOM to be ready before checking
      document.addEventListener('DOMContentLoaded', function() {
        // Immediate check after DOM is ready
        setTimeout(verifyEmployeeActionsLoaded, 50);

        // Secondary check after 500ms
        setTimeout(() => {
          if (!verifyEmployeeActionsLoaded()) {
            console.warn('Attempting to reload employeeActions.js...');
            // Try to reload the script
            const script = document.createElement('script');
            script.src = '/js/employeeActions.js';
            script.onload = () => {
              console.log('employeeActions.js reloaded');
              setTimeout(verifyEmployeeActionsLoaded, 100);
            };
            script.onerror = () => {
              console.error('Failed to reload employeeActions.js');
            };
            document.head.appendChild(script);
          }
        }, 500);
      });
    </script>
  </body>
</html>
