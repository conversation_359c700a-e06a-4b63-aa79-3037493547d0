<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      End Service - <%= employee.firstName %> <%= employee.lastName %>
    </title>
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/employeeProfile.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/mobile-header.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />
    <link rel="stylesheet" href="/css/end-service.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <script src="https://unpkg.com/@phosphor-icons/web"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css"
    />
    <style>
      /* Professional Employee Profile Styles - PandaPayroll Design System */

      /* CSS Variables for Exact employeeProfile Consistency */
      :root {
        --primary-color: #374151;
        --secondary-color: #6b7280;
        --accent-color: #4b5563;
        --background-color: #f9fafb;
        --card-background: #ffffff;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --border-color: #e5e7eb;
        --border-light: #f3f4f6;
        --success-color: #065f46;
        --warning-color: #92400e;
        --danger-color: #991b1b;
        --info-color: #1e40af;
      }

      /* Fix Sidebar Overlap Issue */
      main.main-container {
        flex: 1;
        margin-left: 280px;
        margin-top: 80px;
        padding: 24px;
        width: calc(100vw - 280px);
        min-height: calc(100vh - 80px);
        background: var(--background-color);
        font-family: 'Inter', sans-serif;
      }

      /* Enterprise Tab Navigation - Consistent with employeeProfile */
      #tabs-container {
        margin-bottom: 1.5rem;
      }

      #tabs-section {
        display: flex;
        gap: 0;
        align-items: center;
        flex-wrap: wrap;
        padding: 0;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 2px;
      }

      .tab-button {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: transparent;
        border: none;
        border-right: 1px solid var(--border-color);
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 500;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
        transition: background-color 0.1s ease;
        text-decoration: none;
        border-radius: 0;
        line-height: 1.25;
      }

      .tab-button:last-child {
        border-right: none;
      }

      .tab-button i {
        font-size: 0.875rem;
        line-height: 1;
      }

      .tab-button:hover {
        background: var(--border-light);
        color: var(--text-primary);
      }

      .tab-button.active {
        background: #818cf8;
        color: white;
      }

      .tab-button.warning {
        color: var(--warning-color);
      }

      .tab-button.warning:hover {
        background: #fef3c7;
        color: var(--warning-color);
      }

      .tab-button.danger {
        color: var(--danger-color);
      }

      .tab-button.danger:hover {
        background: #fee2e2;
        color: var(--danger-color);
      }

      /* Enterprise Dropdown Styles - Consistent with employeeProfile */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: var(--card-background);
        border: 1px solid var(--border-color);
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid var(--border-light);
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: var(--border-light);
        color: var(--text-primary);
      }

      .dropdown-content a i {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Enterprise Profile Header Section - Consistent with employeeProfile */
      .title-section {
        background: var(--card-background);
        border-radius: 4px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: none;
      }

      .profile-header {
        display: flex;
        gap: 1.5rem;
        align-items: center;
        margin-bottom: 1.5rem;
      }

      .profile-avatar {
        width: 72px;
        height: 72px;
        border-radius: 4px;
        background: #818cf8;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border: 2px solid var(--border-color);
      }

      .profile-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .avatar-initials {
        color: white;
        font-size: 1.25rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        line-height: 1;
      }

      .profile-info {
        flex: 1;
      }

      .profile-name-section {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .profile-name {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin: 0;
        color: var(--text-primary);
        font-size: 1.5rem;
        font-weight: 600;
        font-family: 'Inter', sans-serif;
      }

      .employee-status {
        display: inline-flex;
        align-items: center;
        gap: 0.375rem;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        background: #dcfce7;
        color: var(--success-color);
        border: 1px solid #bbf7d0;
        line-height: 1.25;
      }

      .employee-status[data-status="Active"] {
        background: #dcfce7;
        color: var(--success-color);
        border-color: #bbf7d0;
      }

      .employee-status[data-status="Inactive"] {
        background: #fee2e2;
        color: var(--danger-color);
        border-color: #fecaca;
      }

      .employee-status[data-status="Serving Notice"] {
        background: #fef3c7;
        color: var(--warning-color);
        border-color: #fde68a;
      }

      .employee-status i {
        font-size: 0.5rem;
        line-height: 1;
      }

      .profile-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
        font-weight: 400;
        text-transform: uppercase;
        letter-spacing: 0.025em;
      }

      .profile-title i {
        color: var(--text-secondary);
        font-size: 0.875rem;
      }

      /* Quick Info Section - Exact Match with employeeProfile */
      .quick-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        gap: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--border-color);
      }

      .info-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
      }

      .info-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: var(--background-color);
        border-radius: 2px;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
      }

      .info-icon i {
        font-size: 0.875rem;
      }

      .info-text {
        display: flex;
        flex-direction: column;
        gap: 0.125rem;
      }

      .info-label {
        color: #9ca3af;
        font-size: 0.6875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .info-value {
        color: var(--text-primary);
        font-size: 0.875rem;
        font-weight: 500;
        font-family: 'Inter', monospace;
      }

      /* Enterprise Tag System - Conservative Business Styling */
      .tag {
        font-size: 0.6875rem;
        padding: 0.125rem 0.375rem;
        border-radius: 2px;
        font-weight: 500;
        white-space: nowrap;
        margin-left: 0.5rem;
        border: 1px solid #d1d5db;
        text-transform: uppercase;
        letter-spacing: 0.025em;
        background-color: #f9fafb;
        color: #374151;
      }

      .tag.age-tag {
        background-color: #f9fafb;
        color: #6b7280;
        border-color: #d1d5db;
        font-size: 0.625rem;
        text-transform: none;
        letter-spacing: normal;
      }

      /* Responsive Design - Exact Match with employeeProfile */
      @media (max-width: 1024px) {
        main.main-container {
          margin-left: 0;
          width: 100%;
          padding: 1rem;
          padding-right: 220px;
        }

        .quick-info {
          grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
          gap: 1rem;
        }
      }

      @media (max-width: 768px) {
        main.main-container {
          padding-right: 1rem;
        }

        .title-section {
          padding: 1.5rem;
        }

        .profile-header {
          flex-direction: column;
          text-align: center;
          gap: 1rem;
        }

        .profile-name {
          flex-direction: column;
          gap: 0.5rem;
          font-size: 1.25rem;
        }

        .quick-info {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        #tabs-section {
          flex-direction: column;
          align-items: stretch;
          gap: 0.5rem;
        }

        .tab-button {
          justify-content: center;
          padding: 0.75rem 1rem;
          min-height: 44px;
          border-right: none;
          border-bottom: 1px solid var(--border-color);
        }

        .tab-button:last-child {
          border-bottom: none;
        }

        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid var(--border-color);
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }

      @media (max-width: 480px) {
        .title-section {
          padding: 1rem;
        }

        .profile-avatar {
          width: 60px;
          height: 60px;
        }

        .avatar-initials {
          font-size: 1.25rem;
        }

        .profile-name {
          font-size: 1.125rem;
        }

        .info-icon {
          width: 32px;
          height: 32px;
        }

        .info-icon i {
          font-size: 0.875rem;
          line-height: 1;
        }

        .tab-button {
          min-height: 48px;
          padding: 0.875rem 1rem;
        }

        .dropdown {
          width: 100%;
        }

        .dropdown-content {
          width: 100%;
          margin-top: 0.25rem;
        }

        .dropdown-content a {
          padding: 1.25rem 1rem;
          font-size: 1rem;
          border-bottom: 1px solid #f1f5f9;
        }

        .dropdown-content a:last-child {
          border-bottom: none;
        }
      }

      /* Touch-friendly interactions */
      @media (hover: none) and (pointer: coarse) {
        .dropdown-content a {
          padding: 1rem;
          min-height: 48px;
        }

        .tab-button {
          min-height: 48px;
          padding: 0.875rem 1rem;
        }
      }
    </style>
  </head>
  <body data-company-code="<%= company.companyCode %>">
    <%- include('partials/header') %>
    <nav><%- include('partials/sidebar') %></nav>
    <main class="main-container">
      <!-- Professional Employee Profile Header -->
      <section class="title-section">
        <div class="profile-header">
          <div class="profile-avatar" title="Employee initials">
            <% if (employee.profileImage) { %>
              <img src="<%= employee.profileImage %>" alt="<%= employee.firstName %>'s profile"
                   onerror="this.onerror=null; this.src=null; this.classList.add('fallback'); this.innerHTML='<%= employee.firstName[0] %><%= employee.lastName[0] %>'">
            <% } else { %>
              <div class="avatar-initials">
                <%= employee.firstName[0] %><%= employee.lastName[0] %>
              </div>
            <% } %>
          </div>

          <div class="profile-info">
            <div class="profile-name-section">
              <h1 class="profile-name">
                <%= employee.firstName %> <%= employee.lastName %>
                <div class="employee-status"
                     data-status="<%= employee.status || 'Active' %>"
                     title="Employee current status">
                  <i class="ph ph-circle-fill"></i>
                  <span><%= employee.status || 'Active' %></span>
                </div>
              </h1>
              <div class="profile-title" title="Employee job title">
                <i class="ph ph-briefcase"></i>
                <%= employee.jobTitle || 'Employee' %>
              </div>
            </div>
          </div>
        </div>

        <div class="quick-info">
          <div class="info-item" title="Employee ID">
            <div class="info-icon">
              <i class="ph ph-identification-card"></i>
            </div>
            <div class="info-text">
              <span class="info-label">Employee ID</span>
              <span class="info-value"><%= employee.companyEmployeeNumber || 'Not Assigned' %></span>
            </div>
          </div>

          <div class="info-item" title="Pay Frequency">
            <div class="info-icon">
              <i class="ph ph-calendar"></i>
            </div>
            <div class="info-text">
              <span class="info-label">Pay Frequency</span>
              <span class="info-value"><%= payFrequency || 'Not Set' %></span>
            </div>
          </div>

          <div class="info-item" title="Department">
            <div class="info-icon">
              <i class="ph ph-buildings"></i>
            </div>
            <div class="info-text">
              <span class="info-label">Department</span>
              <span class="info-value"><%= employee.department || 'Not Assigned' %></span>
            </div>
          </div>
          <%
            // Calculate employee age
            const calculateAge = (dob) => {
              const birthDate = new Date(dob);
              const today = new Date();
              let age = today.getFullYear() - birthDate.getFullYear();
              const monthDiff = today.getMonth() - birthDate.getMonth();
              if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                age--;
              }
              return age;
            };

            const employeeAge = employee.dob ? calculateAge(employee.dob) : 'N/A';
          %>
          <% if (dobFormatted) { %>
              <div class="info-item" title="Date of Birth">
                <div class="info-icon">
                  <i class="ph ph-cake"></i>
                </div>
                <div class="info-text">
                  <span class="info-label">Date of Birth</span>
                  <span class="info-value">
                      <%= dobFormatted %>
                      <% if (employeeAge && employeeAge > 0) { %>
                          <span class="tag age-tag"><%= employeeAge %> years</span>
                      <% } %>
                  </span>
                </div>
              </div>
          <% } %>
        </div>
      </section>

  <!-- Professional Action Navigation -->
  <div class="action-tabs">
    <!-- Payroll Tab -->
    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
      <i class="ph ph-calculator"></i>
      Payroll
    </button>

    <!-- Edit Info Dropdown -->
    <div class="dropdown">
      <button class="tab-button" onclick="toggleDropdown(event)">
        <i class="ph ph-pencil-simple"></i>
        Edit Info
        <i class="ph ph-caret-down"></i>
      </button>
      <div id="dropdownContent" class="dropdown-content">
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
          <i class="ph ph-user"></i>
          Basic Info
        </a>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
          <i class="ph ph-tag"></i>
          Classifications
        </a>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
          <i class="ph ph-file-text"></i>
          Define RFI
        </a>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
          <i class="ph ph-clock"></i>
          Regular Hours
        </a>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
          <i class="ph ph-chart-line"></i>
          ETI
        </a>
        <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
          <i class="ph ph-star"></i>
          Skills Equity
        </a>
      </div>
    </div>

    <!-- Leave Tab -->
    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
      <i class="ph ph-calendar-blank"></i>
      Leave
    </button>

    <!-- End Service Tab -->
    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
       class="tab-button active warning"
       role="button">
      <i class="ph ph-sign-out"></i>
      <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
    </a>

    <!-- Delete Employee Tab -->
    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
      <i class="ph ph-trash"></i>
      Delete Employee
    </button>
  </div>

      <!-- Content Section -->
      <section class="content-section">
        <% if (employee.status === 'Inactive' || employee.status === 'Serving Notice') { %> </p>
        <!-- Status Info Card -->
        <div class="info-card">
          <div class="info-header">
            <i class="ph ph-info"></i>
            <h3>Service Status</h3>
          </div>
          <p>
            The employee's last day of service is
            <strong
              ><%= employee.lastDayOfService ?
              employee.lastDayOfService.toLocaleDateString() : 'Not set'
              %></strong
            >
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <form
            action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/undo-end-of-service"
            method="POST"
          >
            <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
            <button type="submit" class="action-btn btn-secondary">
              <i class="ph ph-arrow-counter-clockwise"></i>
              <span>Cancel/Undo Termination</span>
            </button>
          </form>

          <a
            href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/update-termination-details"
            class="action-btn btn-primary"
          >
            <i class="ph ph-pencil-simple"></i>
            <span>Update Termination Details</span>
          </a>

          <a
            href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/reinstate"
            class="action-btn btn-success"
          >
            <i class="ph ph-user-plus"></i>
            <span>Reinstate Employee</span>
          </a>
        </div>
        <% } else { %>
        <!-- End Service Form -->
        <form
          action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
          method="POST"
          class="form-container"
        >
          <input type="hidden" name="_csrf" value="<%= csrfToken %>" />

          <!-- Form Header -->
          <div class="form-header">
            <i class="ph ph-door"></i>
            <h3>End Employee Service</h3>
            <p class="form-description">
              Please fill in the details below to process the employee's
              termination.
            </p>
          </div>

          <!-- Last Day of Service -->
          <div class="form-group">
            <label for="lastDayOfService">
              <i class="ph ph-calendar"></i>
              Last Day of Service
              <span class="info-label"
                >Must be after Date of Appointment: <%= formattedDOA %></span
              >
            </label>
            <input
              type="date"
              id="lastDayOfService"
              name="lastDayOfService"
              required
              class="form-input"
              min="<%= isoFormattedDOA %>"
            />
          </div>

          <!-- UIF Status Code -->
          <div class="form-group">
            <label for="uifStatusCode">
              <i class="ph ph-identification-card"></i>
              Reason for Termination
            </label>
            <select
              id="uifStatusCode"
              name="uifStatusCode"
              required
              class="form-select"
            >
              <option value="">Select reason for termination</option>
              <option value="2">2. Deceased</option>
              <option value="3">3. Retired</option>
              <option value="4">4. Dismissed</option>
              <option value="5">5. Contract Expired</option>
              <option value="6">6. Resigned</option>
              <option value="7">7. Constructive Dismissal</option>
              <option value="8">8. Insolvency/Liquidation</option>
              <option value="9">9. Maternity</option>
              <option value="9">9. Adoption</option>
              <option value="10">
                10. Long-term leave due to illness (still employed)
              </option>
              <option value="10">10. Long-term illness</option>
              <option value="11">11. Retrenched/Staff Reduction</option>
              <option value="12">12. Transfer to another Branch</option>
              <option value="13">13. Absconded</option>
              <option value="14">14. Business Closed</option>
              <option value="15">15. Death of Domestic Employer</option>
              <option value="16">16. Voluntary Severance Package (VSP)</option>
              <option value="17">17. Reduced Working Time</option>
              <option value="18">18. Commissioning Parental</option>
              <option value="19">19. Parental</option>
            </select>
          </div>

          <!-- Add this right after the UIF Status Code select -->
          <div id="temporaryAbsenceFields" style="display: none">
            <div class="form-group">
              <label for="expectedReturnDate">
                <i class="ph ph-calendar-plus"></i>
                Expected Return Date
                <span class="info-label">Required for UI-2.7 form</span>
              </label>
              <input
                type="text"
                id="expectedReturnDate"
                name="temporaryAbsence[expectedReturnDate]"
                class="form-input flatpickr"
                data-min-date="<%= new Date().toISOString() %>"
              />
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  id="paidDuringAbsence"
                  name="temporaryAbsence[paidDuringAbsence]"
                />
                <span>Paid During Temporary Absence?</span>
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <button
              type="button"
              class="btn btn-secondary"
              onclick="history.back()"
            >
              <i class="ph ph-x"></i>
              Cancel
            </button>
            <button type="submit" class="btn btn-primary">
              <i class="ph ph-check"></i>
              End Service
            </button>
          </div>
        </form>
        <% } %>

        <!-- Service Period History -->
        <% if (employee.status === 'Inactive' || employee.status === 'Serving Notice') { %> </p>
        <div class="history-section">
          <div class="section-header">
            <i class="ph ph-clock-counter-clockwise"></i>
            <h3>Service Period History</h3>
          </div>

          <div class="table-container">
            <table class="service-history-table">
              <thead>
                <tr>
                  <th>Service Period</th>
                  <th>Termination Certificates</th>
                  <th>Edit Certificates</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% if (employee && employee.servicePeriods &&
                employee.servicePeriods.length > 0) { %> <%
                employee.servicePeriods.forEach((period, index) => { %>
                <tr>
                  <td>
                    <div class="period-dates">
                      <i class="ph ph-calendar"></i>
                      <%= period.startDate.toLocaleDateString() %> to <% if
                      (period.endDate) { %> <%=
                      period.endDate.toLocaleDateString() %> <% } else if
                      (employee.status === 'Inactive' || employee.status ===
                      'Serving Notice' && employee.lastDayOfService) { %> <%=
                      employee.lastDayOfService.toLocaleDateString() %> <% }
                      else { %> Present <% } %>
                    </div>
                  </td>
                  <td>
                    <a
                      href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/termination-certificate/<%= index %>/download"
                      class="btn btn-secondary btn-sm"
                      target="_blank"
                    >
                      <i class="ph ph-file-pdf"></i>
                      Generate PDF
                    </a>
                  </td>
                  <td>
                    <a
                      href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/termination-certificate/<%= index %>/edit"
                      class="action-btn btn-primary"
                    >
                      <i class="ph ph-pencil-simple"></i>
                      Edit Certificate
                    </a>
                  </td>
                  <td class="table-actions">
                    <!-- Add other actions if needed -->
                  </td>
                </tr>
                <% }); %> <% } else { %>
                <tr>
                  <td colspan="4" class="empty-state">
                    <i class="ph ph-folder-simple-dotted"></i>
                    <p>No service periods found</p>
                  </td>
                </tr>
                <% } %>
              </tbody>
            </table>
          </div>
        </div>
        <% } %>
      </section>
    </main>

    <script src="/end-service.js"></script>
    <script src="/js/employeeProfile.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const temporaryAbsenceCodes = ["9", "10", "18", "19"];
        const uifSelect = document.getElementById("uifStatusCode");
        const temporaryFields = document.getElementById(
          "temporaryAbsenceFields"
        );
        const expectedReturnDate =
          document.getElementById("expectedReturnDate");
        const lastDayInput = document.getElementById("lastDayOfService");

        // Get employee DOA from server-side
        const employeeDOA = new Date("<%= isoFormattedDOA %>");

        // Initialize flatpickr for last day of service with min date as DOA
        flatpickr(lastDayInput, {
          minDate: employeeDOA,
          dateFormat: "Y-m-d",
          onChange: function (selectedDates) {
            validateDates();
          },
          onOpen: function (selectedDates, dateStr, instance) {
            // Show validation message when calendar opens
            instance.config.errorHandler = function () {
              lastDayInput.setCustomValidity(
                "Last day of service cannot be before date of appointment"
              );
            };
          },
        });

        // Initialize flatpickr for expected return date
        const datePicker = flatpickr(expectedReturnDate, {
          minDate: "today",
          dateFormat: "Y-m-d",
          onChange: function (selectedDates) {
            validateDates();
          },
        });

        // Show/hide fields based on UIF code
        uifSelect.addEventListener("change", function () {
          const showTemporaryFields = temporaryAbsenceCodes.includes(
            this.value
          );
          temporaryFields.style.display = showTemporaryFields
            ? "block"
            : "none";

          if (showTemporaryFields) {
            expectedReturnDate.setAttribute("required", "required");
          } else {
            expectedReturnDate.removeAttribute("required");
          }
        });

        // Enhanced date validation
        function validateDates() {
          const lastDay = new Date(lastDayInput.value);

          // Validate against DOA
          if (lastDay < employeeDOA) {
            lastDayInput.setCustomValidity(
              "Last day of service cannot be before date of appointment"
            );
            return;
          }

          // Clear DOA validation if passed
          lastDayInput.setCustomValidity("");

          // Validate expected return date if applicable
          if (
            temporaryAbsenceCodes.includes(uifSelect.value) &&
            expectedReturnDate.value
          ) {
            const returnDate = new Date(expectedReturnDate.value);

            if (returnDate <= lastDay) {
              expectedReturnDate.setCustomValidity(
                "Expected return date must be after last day of service"
              );
            } else {
              expectedReturnDate.setCustomValidity("");
            }
          }
        }

        // Add form submit validation
        const form = document.querySelector("form");
        form.addEventListener("submit", function (event) {
          const lastDay = new Date(lastDayInput.value);

          if (lastDay < employeeDOA) {
            event.preventDefault();
            lastDayInput.setCustomValidity(
              "Last day of service cannot be before date of appointment"
            );
            lastDayInput.reportValidity();
          }
        });

        // Add validation to last day of service
        lastDayInput.addEventListener("change", validateDates);
      });

      // Dropdown toggle function
      function toggleDropdown(event) {
        event.preventDefault();
        event.stopPropagation();

        const dropdown = event.target.closest('.dropdown');
        const dropdownContent = dropdown.querySelector('.dropdown-content');

        // Close all other dropdowns
        document.querySelectorAll('.dropdown-content').forEach(content => {
          if (content !== dropdownContent) {
            content.classList.remove('show');
          }
        });

        // Toggle current dropdown
        dropdownContent.classList.toggle('show');
      }

      // Close dropdown when clicking outside
      document.addEventListener('click', function(event) {
        if (!event.target.closest('.dropdown')) {
          document.querySelectorAll('.dropdown-content').forEach(content => {
            content.classList.remove('show');
          });
        }
      });

      // Delete employee function - using the same implementation as employeeProfile.ejs
      // This function is now loaded from employeeActions.js

      // Wrapper function to safely handle delete employee action
      function handleDeleteEmployee(employeeId) {
        if (typeof window.confirmDeleteEmployee === 'function') {
          window.confirmDeleteEmployee(employeeId);
        } else {
          console.error('confirmDeleteEmployee function not available. Please refresh the page.');
          alert('Delete function not available. Please refresh the page and try again.');
        }
      }

      // Make function globally available
      window.handleDeleteEmployee = handleDeleteEmployee;
    </script>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>
  </body>
</html>
