<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Regular Hours - <%= employee.firstName %> <%= employee.lastName %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/styles.css" />
    <link rel="stylesheet" href="/regularhours.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <style>
        .working-days-grid {
            display: grid;
            gap: 1rem;
            padding: 1.5rem;
            background: #f8fafc;
            border-radius: 0.75rem;
            margin-top: 1rem;
        }

        .day-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.75rem;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            transition: all 0.2s;
        }

        .day-row:hover {
            border-color: #6366f1;
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid #e2e8f0;
            border-radius: 0.375rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .checkbox-group input[type="checkbox"]:checked {
            background-color: #6366f1;
            border-color: #6366f1;
        }

        .checkbox-group label {
            font-weight: 500;
            color: #1e293b;
            cursor: pointer;
        }

        .day-type-select {
            padding: 0.5rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            color: #1e293b;
            background: #ffffff;
            min-width: 120px;
            transition: all 0.2s;
        }

        .day-type-select:focus {
            border-color: #6366f1;
            outline: none;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .days-per-week-display {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 0.5rem;
            margin-top: 1rem;
        }

        #fullDaysPerWeek {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
        }

        .override-button {
            padding: 0.5rem 1rem;
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 0.375rem;
            color: #6366f1;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .override-button:hover {
            background: #f8fafc;
            border-color: #6366f1;
        }

        .override-button i {
            font-size: 1.25rem;
        }

        .help-text {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .help-text i {
            color: #6366f1;
        }

        /* Add Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 0.75rem;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            animation: modalSlideIn 0.3s ease-out;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e2e8f0;
            display: flex;
            justify-content: flex-end;
            gap: 1rem;
        }

        .close-modal {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #64748b;
            transition: color 0.2s;
        }

        .close-modal:hover {
            color: #1e293b;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Add styles for the modal input */
        #fullDaysOverride {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.2s;
        }

        #fullDaysOverride:focus {
            border-color: #6366f1;
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            outline: none;
        }

        /* Add styles for the help text */
        .help-text {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .help-text i {
            color: #6366f1;
        }

        /* Dropdown Styles for Edit Info */
        .dropdown {
          position: relative;
          display: inline-block;
        }

        .dropdown-content {
          display: none;
          position: absolute;
          top: 100%;
          left: 0;
          min-width: 220px;
          background: #ffffff;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
          z-index: 1000;
          overflow: hidden;
          max-height: 300px;
          overflow-y: auto;
        }

        .dropdown-content a {
          display: flex;
          align-items: center;
          gap: 0.75rem;
          padding: 0.75rem 1rem;
          color: #6b7280;
          text-decoration: none;
          font-size: 0.875rem;
          font-weight: 400;
          transition: background-color 0.1s ease;
          border-bottom: 1px solid #f3f4f6;
        }

        .dropdown-content a:last-child {
          border-bottom: none;
        }

        .dropdown-content a:hover {
          background: #f3f4f6;
          color: #111827;
        }

        .dropdown-content a.active {
          background: #818cf8;
          color: white;
        }

        .dropdown-content a i {
          color: #6b7280;
          font-size: 0.875rem;
        }

        .dropdown-content a.active i {
          color: white;
        }

        .dropdown-content.show {
          display: block;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
          .dropdown-content {
            position: relative;
            display: block;
            margin-top: 0.5rem;
            box-shadow: none;
            border: 1px solid #e5e7eb;
            border-radius: 2px;
            width: 100%;
            left: 0;
            right: 0;
          }

          .dropdown-content a {
            padding: 1rem;
            font-size: 0.875rem;
            min-height: 44px;
            display: flex;
            align-items: center;
          }
        }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar') %>
        <div class="content-wrapper">
            <%- include('partials/header') %>

            <main class="main-container" style="padding-top: 12rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours" class="active">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <form id="regularHoursForm"
                      method="post"
                      action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                    
                    <!-- Add CSRF Token -->
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                    
                    <!-- Working Hours Configuration Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-clock"></i> Working Hours Configuration</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" 
                                           id="hourlyPaid" 
                                           name="hourlyPaid" 
                                           <%= employee.regularHours?.hourlyPaid ? 'checked' : '' %> />
                                    <label for="hourlyPaid">Hourly paid</label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="hoursPerDay">Hours per day</label>
                                <input type="number" 
                                       id="hoursPerDay" 
                                       name="hoursPerDay" 
                                       step="0.1" 
                                       value="<%= employee.regularHours?.hoursPerDay || 8.0 %>" />
                            </div>

                            <div class="form-group">
                                <label for="schedule">Schedule</label>
                                <select id="schedule" name="schedule">
                                    <option value="Fixed" <%= employee.regularHours?.schedule === 'Fixed' ? 'selected' : '' %>>Fixed</option>
                                    <option value="Casual/Temp" <%= employee.regularHours?.schedule === 'Casual/Temp' ? 'selected' : '' %>>Casual/Temp</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Working Days Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-calendar"></i> Working Days</h2>
                        </div>
                        <div class="card-content">
                            <div class="working-days-grid">
                                <% ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].forEach(day => { %>
                                    <div class="day-row">
                                        <div class="checkbox-group">
                                            <input type="checkbox" 
                                                   name="workingDays[]" 
                                                   value="<%= day %>" 
                                                   id="day<%= day %>"
                                                   <%= (employee.regularHours?.workingDays || []).includes(day) ? 'checked' : '' %> />
                                            <label for="day<%= day %>"><%= day %></label>
                                        </div>
                                        <select name="dayType[<%= day %>]" class="day-type-select">
                                            <option value="Normal Day" <%= employee.regularHours?.dayTypes?.[day] === 'Normal Day' ? 'selected' : '' %>>Normal Day</option>
                                            <option value="Half Day" <%= employee.regularHours?.dayTypes?.[day] === 'Half Day' ? 'selected' : '' %>>Half Day</option>
                                        </select>
                                    </div>
                                <% }); %>
                            </div>
                        </div>
                    </div>

                    <!-- Full Days Calculation Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-calculator"></i> Full Days Calculation</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label>Full days per week</label>
                                <div class="days-per-week-display">
                                    <span id="fullDaysPerWeek"><%= employee.regularHours?.fullDaysPerWeek || 5.0 %></span>
                                    <button type="button" id="overrideButton" class="override-button">
                                        <i class="ph ph-pencil-simple"></i>
                                        Override
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal for Full Days Override -->
                    <div id="overrideModal" class="modal" style="display: none;">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h3>Override Full Days Per Week</h3>
                                <button type="button" class="close-modal">&times;</button>
                            </div>
                            <div class="modal-body">
                                <div class="form-group">
                                    <label for="fullDaysOverride">Full Days Per Week</label>
                                    <input type="number" 
                                           id="fullDaysOverride" 
                                           step="0.5" 
                                           min="0" 
                                           max="7" 
                                           value="<%= employee.regularHours?.fullDaysPerWeek || 5.0 %>" />
                                </div>
                                <div class="help-text">
                                    <i class="ph ph-info"></i>
                                    Value must be between 0 and 7 in increments of 0.5
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary close-modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="saveOverride">Save</button>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" 
                                onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('regularHoursForm');
            const overrideButton = document.getElementById('overrideButton');
            const fullDaysPerWeekSpan = document.getElementById('fullDaysPerWeek');
            const workingDaysCheckboxes = document.querySelectorAll('input[name="workingDays[]"]');

            workingDaysCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateFullDaysPerWeek);
            });

            function updateFullDaysPerWeek() {
                const checkedDays = document.querySelectorAll('input[name="workingDays[]"]:checked').length;
                fullDaysPerWeekSpan.textContent = checkedDays.toFixed(1);
            }

            overrideButton.addEventListener('click', function() {
                const newValue = prompt('Enter new value for full days per week:', fullDaysPerWeekSpan.textContent);
                if (newValue !== null && !isNaN(newValue)) {
                    fullDaysPerWeekSpan.textContent = parseFloat(newValue).toFixed(1);
                }
            });
        });
    </script>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
        // Dropdown toggle function
        function toggleDropdown(event) {
          event.preventDefault();
          event.stopPropagation();

          const dropdown = event.target.closest('.dropdown');
          const dropdownContent = dropdown.querySelector('.dropdown-content');

          // Close all other dropdowns
          document.querySelectorAll('.dropdown-content').forEach(content => {
            if (content !== dropdownContent) {
              content.classList.remove('show');
            }
          });

          // Toggle current dropdown
          dropdownContent.classList.toggle('show');
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
          if (!event.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-content').forEach(content => {
              content.classList.remove('show');
            });
          }
        });

        // Delete employee function
        function handleDeleteEmployee(employeeId) {
          if (typeof window.confirmDeleteEmployee === 'function') {
            window.confirmDeleteEmployee(employeeId);
          } else {
            console.error('confirmDeleteEmployee function not available. Please refresh the page.');
            alert('Delete function not available. Please refresh the page and try again.');
          }
        }

        // Define missing functions to prevent errors
        window.updatePageIndicator = window.updatePageIndicator || function() {
          // No-op function for pages that don't have page indicators
        };
    </script>
</body>
</html>
