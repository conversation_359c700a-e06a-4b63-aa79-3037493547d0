<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skills & Equity | <%= company.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/skillsEquity.css">
    <link rel="stylesheet" href="/css/employeeManagement.css">
    <link rel="stylesheet" href="/css/header.css">
    <link rel="stylesheet" href="/css/sidebar.css">

    <style>
      /* Dropdown Styles for Edit Info */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #6b7280;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid #f3f4f6;
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: #f3f4f6;
        color: #111827;
      }

      .dropdown-content a.active {
        background: #818cf8;
        color: white;
      }

      .dropdown-content a i {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .dropdown-content a.active i {
        color: white;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar', { user: user, company: company }) %>
        <div class="content-wrapper">
            <%- include('partials/header', { user: user }) %>

            <main class="main-container" style="padding-top: 12rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity" class="active">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <!-- Form Card -->
                <form class="action-card" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity" method="post">
                    <!-- Add CSRF token hidden input -->
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>">
                    
                    <!-- Personal Information Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-user"></i>
                            Personal Information
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="form-group">
                            <label for="gender">
                                <i class="ph ph-gender-intersex"></i>
                                Gender
                            </label>
                            <select id="gender" name="gender" class="filter-select" required>
                                <option value="">Select Gender</option>
                                <option value="Male" <%= employee.gender === 'Male' ? 'selected' : '' %>>Male</option>
                                <option value="Female" <%= employee.gender === 'Female' ? 'selected' : '' %>>Female</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="race">
                                <i class="ph ph-users"></i>
                                Race
                            </label>
                            <select id="race" name="race" class="filter-select" required>
                                <option value="">Select Race</option>
                                <option value="African" <%= employee.race === 'African' ? 'selected' : '' %>>African</option>
                                <option value="Coloured" <%= employee.race === 'Coloured' ? 'selected' : '' %>>Coloured</option>
                                <option value="Indian" <%= employee.race === 'Indian' ? 'selected' : '' %>>Indian</option>
                                <option value="White" <%= employee.race === 'White' ? 'selected' : '' %>>White</option>
                            </select>
                        </div>
                    </div>

                    <!-- Occupation Details Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-briefcase"></i>
                            Occupation Details
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="form-group">
                            <label for="occupationLevel">Occupation Level</label>
                            <select id="occupationLevel" name="occupationLevel" required>
                                <option value="">Select Occupation Level</option>
                                <option value="Top Management" <%= employee.occupationLevel === 'Top Management' ? 'selected' : '' %>>Top Management</option>
                                <option value="Senior Management" <%= employee.occupationLevel === 'Senior Management' ? 'selected' : '' %>>Senior Management</option>
                                <option value="Prof. specialists and mid-management" <%= employee.occupationLevel === 'Prof. specialists and mid-management' ? 'selected' : '' %>>Prof. specialists and mid-management</option>
                                <option value="Skilled workers, junior management, supervisors" <%= employee.occupationLevel === 'Skilled workers, junior management, supervisors' ? 'selected' : '' %>>Skilled workers, junior management, supervisors</option>
                                <option value="Semi-skilled and discretionary decision making" <%= employee.occupationLevel === 'Semi-skilled and discretionary decision making' ? 'selected' : '' %>>Semi-skilled and discretionary decision making</option>
                                <option value="Unskilled and defined decision making" <%= employee.occupationLevel === 'Unskilled and defined decision making' ? 'selected' : '' %>>Unskilled and defined decision making</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="occupationCategory">Occupation Category</label>
                            <select id="occupationCategory" name="occupationCategory" required>
                                <option value="">Select Occupation Category</option>
                                <option value="Legislators, senior officials and managers" <%= employee.occupationCategory === 'Legislators, senior officials and managers' ? 'selected' : '' %>>Legislators, senior officials and managers</option>
                                <option value="Professionals" <%= employee.occupationCategory === 'Professionals' ? 'selected' : '' %>>Professionals</option>
                                <option value="Technicians and associate professionals" <%= employee.occupationCategory === 'Technicians and associate professionals' ? 'selected' : '' %>>Technicians and associate professionals</option>
                                <option value="Clerks" <%= employee.occupationCategory === 'Clerks' ? 'selected' : '' %>>Clerks</option>
                                <option value="Service and sales workers" <%= employee.occupationCategory === 'Service and sales workers' ? 'selected' : '' %>>Service and sales workers</option>
                                <option value="Skilled agricultural and fishery workers" <%= employee.occupationCategory === 'Skilled agricultural and fishery workers' ? 'selected' : '' %>>Skilled agricultural and fishery workers</option>
                                <option value="Craft and related trades workers" <%= employee.occupationCategory === 'Craft and related trades workers' ? 'selected' : '' %>>Craft and related trades workers</option>
                                <option value="Plant and machine operators and assemblers" <%= employee.occupationCategory === 'Plant and machine operators and assemblers' ? 'selected' : '' %>>Plant and machine operators and assemblers</option>
                                <option value="Elementary occupations" <%= employee.occupationCategory === 'Elementary occupations' ? 'selected' : '' %>>Elementary occupations</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="jobValue">Job Value</label>
                            <select id="jobValue" name="jobValue">
                                <option value="">Select Job Value</option>
                                <option value="Operational / Core Function" <%= employee.jobValue === 'Operational / Core Function' ? 'selected' : '' %>>Operational / Core Function</option>
                                <option value="Support Function" <%= employee.jobValue === 'Support Function' ? 'selected' : '' %>>Support Function</option>
                            </select>
                        </div>
                    </div>

                    <!-- Location Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-map-pin"></i>
                            Location
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="form-group">
                            <label for="province">Province</label>
                            <select id="province" name="province">
                                <option value="">Select Province</option>
                                <option value="Gauteng" <%= employee.province === 'Gauteng' ? 'selected' : '' %>>Gauteng</option>
                                <option value="Eastern Cape" <%= employee.province === 'Eastern Cape' ? 'selected' : '' %>>Eastern Cape</option>
                                <option value="Free State" <%= employee.province === 'Free State' ? 'selected' : '' %>>Free State</option>
                                <option value="Kwa-Zulu Natal" <%= employee.province === 'Kwa-Zulu Natal' ? 'selected' : '' %>>Kwa-Zulu Natal</option>
                                <option value="Mpumalanga" <%= employee.province === 'Mpumalanga' ? 'selected' : '' %>>Mpumalanga</option>
                                <option value="Northern Cape" <%= employee.province === 'Northern Cape' ? 'selected' : '' %>>Northern Cape</option>
                                <option value="Limpopo" <%= employee.province === 'Limpopo' ? 'selected' : '' %>>Limpopo</option>
                                <option value="North West" <%= employee.province === 'North West' ? 'selected' : '' %>>North West</option>
                                <option value="Western Cape" <%= employee.province === 'Western Cape' ? 'selected' : '' %>>Western Cape</option>
                            </select>
                        </div>
                    </div>

                    <!-- Additional Information Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-info"></i>
                            Additional Information
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="disabled" name="disabled" <%= employee.disabled ? 'checked' : '' %>>
                                <span class="checkbox-text">Person with Disability</span>
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="foreignNational" name="foreignNational" <%= employee.foreignNational ? 'checked' : '' %>>
                                <span class="checkbox-text">Foreign National</span>
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="notRSACitizen" name="notRSACitizen" <%= employee.notRSACitizen ? 'checked' : '' %>>
                                <span class="checkbox-text">Not RSA Citizen</span>
                            </label>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="card-content" style="border-top: 1px solid #e2e8f0;">
                        <div class="button-group">
                            <button type="button" class="btn btn-secondary" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                                <i class="ph ph-x"></i>
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="ph ph-check"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
    // Dropdown toggle function
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdown = event.target.closest('.dropdown');
      const dropdownContent = dropdown.querySelector('.dropdown-content');

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-content').forEach(content => {
        if (content !== dropdownContent) {
          content.classList.remove('show');
        }
      });

      // Toggle current dropdown
      dropdownContent.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(content => {
          content.classList.remove('show');
        });
      }
    });

    // Delete employee function
    function handleDeleteEmployee(employeeId) {
      if (typeof window.confirmDeleteEmployee === 'function') {
        window.confirmDeleteEmployee(employeeId);
      } else {
        console.error('confirmDeleteEmployee function not available. Please refresh the page.');
        alert('Delete function not available. Please refresh the page and try again.');
      }
    }

    // Define missing functions to prevent errors
    window.updatePageIndicator = window.updatePageIndicator || function() {
      // No-op function for pages that don't have page indicators
    };
    </script>
</body>
</html>