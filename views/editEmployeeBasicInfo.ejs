<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <title>Edit Employee Information - <%= employee.firstName %> <%= employee.lastName %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    
    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />
    <link rel="stylesheet" href="/css/mobile-header.min.css" />
    <link rel="stylesheet" href="/css/mobile-employee-profile.css" />
    <link rel="stylesheet" href="/css/mobile-nav.css" />


    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">

    <!-- Add CSRF token -->
    <meta name="csrf-token" content="<%= csrfToken %>" />

    <style>
      /* Dropdown Styles for Edit Info */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #6b7280;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid #f3f4f6;
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: #f3f4f6;
        color: #111827;
      }

      .dropdown-content a.active {
        background: #818cf8;
        color: white;
      }

      .dropdown-content a i {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .dropdown-content a.active i {
        color: white;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }
    </style>
  </head>
  <body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar') %>
        <div class="content-wrapper">
            <%- include('partials/header') %>

            <main class="main-container" style="padding-top: 16rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit" class="active">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <form id="editEmployeeForm" method="post"
                      action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                    
                    <!-- Personal Details Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-user"></i> Personal Details</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="firstName">First Names</label>
                                    <input type="text" id="firstName" name="firstName" 
                                           value="<%= employee.firstName || '' %>" required />
                                </div>
                                <div class="form-group">
                                    <label for="lastName">Last Name</label>
                                    <input type="text" id="lastName" name="lastName" 
                                           value="<%= employee.lastName || '' %>" required />
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="dob">Date of Birth</label>
                                    <div class="input-icon">
                                        <i class="ph ph-calendar"></i>
                                        <!-- Debug: DOB value = <%= employee.dob %>, type = <%= typeof employee.dob %> -->
                                        <input type="date" id="dob" name="dob"
                                               value="<%= employee.dob ? moment(employee.dob).format('YYYY-MM-DD') : '' %>" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="idType">Identification Type</label>
                                    <div class="select-wrapper">
                                        <select id="idType" name="idType" required>
                                            <option value="">Select ID Type</option>
                                            <option value="rsa" <%= (employee.personalDetails?.idType === 'rsa' || employee.idType === 'rsa') ? 'selected' : '' %>>RSA ID</option>
                                            <option value="passport" <%= (employee.personalDetails?.idType === 'passport' || employee.idType === 'passport') ? 'selected' : '' %>>Passport</option>
                                            <option value="none" <%= (employee.personalDetails?.idType === 'none' || employee.personalDetails?.idType === 'other' || employee.idType === 'none') ? 'selected' : '' %>>None</option>
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <div class="input-icon">
                                        <i class="ph ph-envelope"></i>
                                        <input type="email" id="email" name="email" 
                                               value="<%= employee.email || '' %>" 
                                               required 
                                               placeholder="Enter email address" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="mobileNumber">Mobile Number</label>
                                    <div class="input-icon">
                                        <i class="ph ph-phone"></i>
                                        <input type="tel" id="mobileNumber" 
                                               name="personalDetails.mobileNumber" 
                                               value="<%= employee.personalDetails?.mobileNumber || '' %>" 
                                               placeholder="Enter mobile number" 
                                               pattern="[0-9]{10,15}"
                                               title="Please enter a valid phone number (10-15 digits)"
                                               onchange="console.log('Mobile number changed:', this.value)" />
                                    </div>
                                    <small class="form-hint">Format: 10-15 digits (e.g., 0831234567)</small>
                                </div>
                            </div>

                            <div id="idNumberContainer" class="form-group" style="display: <%= (employee.personalDetails?.idType === 'rsa' || employee.idType === 'rsa') ? 'block' : 'none' %>;">
                                <label for="idNumber">Identity Number</label>
                                <div class="input-icon">
                                    <i class="ph ph-identification-card"></i>
                                    <input type="text" id="idNumber" name="idNumber"
                                           value="<%= employee.personalDetails?.idNumber || employee.idNumber || '' %>"
                                           placeholder="Enter ID number" />
                                </div>
                            </div>

                            <div id="passportNumberContainer" class="form-group" style="display: <%= (employee.personalDetails?.idType === 'passport' || employee.idType === 'passport') ? 'block' : 'none' %>;">
                                <label for="passportNumber">Passport Number</label>
                                <div class="input-icon">
                                    <i class="ph ph-passport"></i>
                                    <input type="text" id="passportNumber" name="passportNumber"
                                           value="<%= employee.personalDetails?.passportNumber || employee.passportNumber || '' %>"
                                           placeholder="Enter passport number" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Employment Details Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-briefcase"></i> Employment Details</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="jobTitle">Job Title</label>
                                    <div class="input-icon">
                                        <i class="ph ph-identification-badge"></i>
                                        <input type="text" id="jobTitle" name="jobTitle" 
                                               value="<%= employee.jobTitle || '' %>" required />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="department">Department</label>
                                    <div class="input-icon">
                                        <i class="ph ph-buildings"></i>
                                        <input type="text" id="department" name="department" 
                                               value="<%= employee.department || '' %>" />
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="costCentre">Cost Centre</label>
                                    <div class="input-icon">
                                        <i class="ph ph-chart-pie"></i>
                                        <input type="text" id="costCentre" name="costCentre" 
                                               value="<%= employee.costCentre || '' %>" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="payFrequency">Pay Frequency</label>
                                    <div class="select-wrapper">
                                      <select id="payFrequency" name="payFrequency" required>
                                        <option value="">Select Pay Frequency</option>
                                        <% if (payFrequencies && payFrequencies.length > 0) { %>
                                          <% payFrequencies.forEach(function(pf) { %>
                                            <option 
                                              value="<%= pf._id %>" 
                                              <%= employee.payFrequency && employee.payFrequency._id && 
                                                  employee.payFrequency._id.toString() === pf._id.toString() ? 'selected' : '' %>
                                              data-frequency="<%= pf.frequency %>"
                                              data-last-day="<%= pf.lastDayOfPeriod %>">
                                              <%= pf.name %>
                                            </option>
                                          <% }); %>
                                        <% } else { %>
                                          <option value="" disabled>No pay frequencies available</option>
                                        <% } %>
                                      </select>
                                      <i class="ph ph-caret-down"></i>
                                    </div>
                                    <div class="input-hint">
                                      <small>This will determine how payroll periods are generated</small>
                                    </div>
                                  </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doa">Date of Appointment</label>
                                    <div class="input-icon">
                                        <i class="ph ph-calendar"></i>
                                        <input type="date" id="doa" name="doa"
                                               value="<%= employee.doa ? moment(employee.doa).format('YYYY-MM-DD') : '' %>"
                                               placeholder="Select date">
                                    </div>
                                </div>
                            </div>

                            <!-- OID Eligibility Section -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="oidEligible">OID Eligible</label>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="oidEligible" name="oidEligible" 
                                               <%= employee.oidEligible ? 'checked' : '' %>>
                                        <label for="oidEligible" class="toggle-label"></label>
                                    </div>
                                </div>
                                <div class="form-group" id="oidExemptReasonGroup" style="display: <%= employee.oidEligible ? 'none' : 'block' %>;">
                                    <label for="oidExemptReason">OID Exempt Reason</label>
                                    <div class="input-icon">
                                        <i class="ph ph-note"></i>
                                        <input type="text" id="oidExemptReason" name="oidExemptReason"
                                               value="<%= employee.oidExemptReason || '' %>"
                                               placeholder="Reason for OID exemption">
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="paymentMethod">Payment Method</label>
                                    <div class="select-wrapper">
                                        <select id="paymentMethod" name="paymentMethod">
                                            <option value="EFT" <%= employee.paymentMethod === 'EFT' ? 'selected' : '' %>>EFT</option>
                                            <option value="Cash" <%= employee.paymentMethod === 'Cash' ? 'selected' : '' %>>Cash</option>
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Banking Details Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-bank"></i> Banking Details</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="bankName">Bank Name</label>
                                    <div class="select-wrapper">
                                        <select id="bankName" name="bankName" data-current-bank="<%= employee.bank || '' %>">
                                            <option value="">Select Bank</option>
                                            <!-- Banks will be populated via JavaScript -->
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="accountHolder">Account Holder Type</label>
                                    <div class="select-wrapper">
                                        <select id="accountHolder" name="accountHolder" required>
                                            <option value="">Select Account Holder Type</option>
                                            <option value="Own" <%= employee.accountHolder === 'Own' ? 'selected' : '' %>>Own</option>
                                            <option value="Joint" <%= employee.accountHolder === 'Joint' ? 'selected' : '' %>>Joint</option>
                                            <option value="Third Party" <%= employee.accountHolder === 'Third Party' ? 'selected' : '' %>>Third Party</option>
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="accountNumber">Account Number</label>
                                    <div class="input-icon">
                                        <i class="ph ph-hash"></i>
                                        <input type="text" id="accountNumber" name="accountNumber" 
                                               value="<%= employee.accountNumber || '' %>" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="accountType">Account Type</label>
                                    <div class="select-wrapper">
                                        <select id="accountType" name="accountType">
                                            <option value="">Select Account Type</option>
                                            <option value="Current" <%= employee.accountType === 'Current' ? 'selected' : '' %>>Current</option>
                                            <option value="Savings" <%= employee.accountType === 'Savings' ? 'selected' : '' %>>Savings</option>
                                            <option value="Transmission" <%= employee.accountType === 'Transmission' ? 'selected' : '' %>>Transmission</option>
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="branchCode">Branch Code</label>
                                    <div class="input-icon">
                                        <i class="ph ph-git-branch"></i>
                                        <input type="text" id="branchCode" name="branchCode" 
                                               value="<%= employee.branchCode || '' %>" />
                                        <div class="input-hint">
                                            <small>Branch code will auto-populate based on selected bank</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Details Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-house"></i> Residential Address</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="streetAddress">Street Address</label>
                                    <div class="input-icon">
                                        <i class="ph ph-map-pin"></i>
                                        <input type="text" id="streetAddress" name="streetAddress" 
                                               value="<%= employee.streetAddress || '' %>" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="suburb">Suburb</label>
                                    <div class="input-icon">
                                        <i class="ph ph-tree"></i>
                                        <input type="text" id="suburb" name="suburb" 
                                               value="<%= employee.suburb || '' %>" />
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="city">City</label>
                                    <div class="input-icon">
                                        <i class="ph ph-buildings"></i>
                                        <input type="text" id="city" name="city" 
                                               value="<%= employee.city || '' %>" />
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="postalCode">Postal Code</label>
                                    <div class="input-icon">
                                        <i class="ph ph-envelope"></i>
                                        <input type="text" id="postalCode" name="postalCode" 
                                               value="<%= employee.postalCode || '' %>" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" 
                                onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                            <i class="ph ph-x"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ph ph-check"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="/js/editEmployeeBasicInfo.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const idTypeSelect = document.getElementById('idType');
        const idNumberContainer = document.getElementById('idNumberContainer');
        const passportNumberContainer = document.getElementById('passportNumberContainer');

        functioateIdFields() {
            const selectedType = idTypeSelect.value;
            idNumberContainer.style.display = selectedType === 'rsa' ? 'block' : 'none';
            passportNumberContainer.style.display = selectedType === 'passport' ? 'block' : 'none';
        }

        idTypeSelect.addEventListener('change', updateIdFields);

        const mobileInput = document.getElementById('mobileNumber');
        if (mobileInput) {
            console.log('Mobile number field initial value:', mobileInput.value);
        }
    });
    </script>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
    // Dropdown toggle function
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdown = event.target.closest('.dropdown');
      const dropdownContent = dropdown.querySelector('.dropdown-content');

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-content').forEach(content => {
        if (content !== dropdownContent) {
          content.classList.remove('show');
        }
      });

      // Toggle current dropdown
      dropdownContent.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(content => {
          content.classList.remove('show');
        });
      }
    });

    // Delete employee function
    function handleDeleteEmployee(employeeId) {
      if (typeof window.confirmDeleteEmployee === 'function') {
        window.confirmDeleteEmployee(employeeId);
      } else {
        console.error('confirmDeleteEmployee function not available. Please refresh the page.');
        alert('Delete function not available. Please refresh the page and try again.');
      }
    }

    // Define missing functions to prevent errors
    window.updatePageIndicator = window.updatePageIndicator || function() {
      // No-op function for pages that don't have page indicators
    };
    </script>
  </body>
</html>
