<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>RFI Configuration - <%= employee.firstName %> <%= employee.lastName %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/styles.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Add CSRF token -->
    <meta name="csrf-token" content="<%= csrfToken %>" />

    <!-- Add this to the existing style section in the head -->
    <style>
    .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .checkbox-wrapper {
        position: relative;
        background: #fff;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        padding: 0.75rem;
        transition: all 0.2s ease;
    }

    .checkbox-wrapper:hover {
        border-color: #dee2e6;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .checkbox-wrapper input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
    }

    .checkbox-wrapper label {
        display: flex;
        align-items: center;
        margin: 0;
        cursor: pointer;
        font-weight: 500;
        color: #2c3e50;
    }

    .checkbox-wrapper label i {
        width: 20px;
        height: 20px;
        border: 2px solid #cbd5e1;
        border-radius: 4px;
        margin-right: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        color: transparent;
        transition: all 0.2s ease;
    }

    .checkbox-wrapper input[type="checkbox"]:checked + label i {
        background-color: #3b82f6;
        border-color: #3b82f6;
        color: white;
    }

    .checkbox-wrapper .amount {
        margin-left: auto;
        font-size: 0.875rem;
        color: #64748b;
    }

    .helper-text {
        padding: 0.75rem;
        background: #f8fafc;
        border-radius: 6px;
        color: #64748b;
    }

    .helper-text i {
        margin-right: 0.5rem;
        color: #3b82f6;
    }

    /* Dropdown Styles for Edit Info */
    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-content {
      display: none;
      position: absolute;
      top: 100%;
      left: 0;
      min-width: 220px;
      background: #ffffff;
      border: 1px solid #e5e7eb;
      border-radius: 2px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      z-index: 1000;
      overflow: hidden;
      max-height: 300px;
      overflow-y: auto;
    }

    .dropdown-content a {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      color: #6b7280;
      text-decoration: none;
      font-size: 0.875rem;
      font-weight: 400;
      transition: background-color 0.1s ease;
      border-bottom: 1px solid #f3f4f6;
    }

    .dropdown-content a:last-child {
      border-bottom: none;
    }

    .dropdown-content a:hover {
      background: #f3f4f6;
      color: #111827;
    }

    .dropdown-content a.active {
      background: #818cf8;
      color: white;
    }

    .dropdown-content a i {
      color: #6b7280;
      font-size: 0.875rem;
    }

    .dropdown-content a.active i {
      color: white;
    }

    .dropdown-content.show {
      display: block;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
      .dropdown-content {
        position: relative;
        display: block;
        margin-top: 0.5rem;
        box-shadow: none;
        border: 1px solid #e5e7eb;
        border-radius: 2px;
        width: 100%;
        left: 0;
        right: 0;
      }

      .dropdown-content a {
        padding: 1rem;
        font-size: 0.875rem;
        min-height: 44px;
        display: flex;
        align-items: center;
      }
    }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar') %>
        <div class="content-wrapper">
            <%- include('partials/header') %>

            <main class="main-container" style="padding-top: 12rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI" class="active">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <form id="rfiForm" method="post"
                      action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                    
                    <!-- RFI Method Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-calculator"></i> RFI Calculation Method</h2>
                        </div>
                        <div class="card-content">
                            <div class="form-group">
                                <label for="rfiOptions">Select RFI Calculation Method</label>
                                <div class="select-wrapper">
                                    <select id="rfiOptions" name="rfiOption" required>
                                        <option value="">Select calculation method</option>
                                        <option value="selectedIncome" <%= rfiConfig.option === 'selectedIncome' ? 'selected' : '' %>>
                                            Selected Income Components (100% Inclusion)
                                        </option>
                                        <option value="percentagePerIncome" <%= rfiConfig.option === 'percentagePerIncome' ? 'selected' : '' %>>
                                            Custom Percentage per Component
                                        </option>
                                    </select>
                                    <i class="ph ph-caret-down"></i>
                                    <div class="input-hint">
                                        <div class="hint-content">
                                            <div class="hint-item">
                                                <i class="ph ph-check-circle"></i>
                                                <strong>Selected Income Components:</strong> 
                                                <span>Include specific income components at 100% of their value in RFI calculation</span>
                                            </div>
                                            <div class="hint-item">
                                                <i class="ph ph-percent"></i>
                                                <strong>Custom Percentage per Component:</strong>
                                                <span>Define custom inclusion percentages for each income component</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Income Card -->
                    <div id="selectedIncomeCard" class="edit-card" style="display: <%= rfiConfig.option === 'selectedIncome' ? 'block' : 'none' %>">
                        <div class="card-header">
                            <h2><i class="ph ph-list-checks"></i> Selected Incomes</h2>
                        </div>
                        <div class="card-content">
                            <div class="checkbox-grid">
                                <% payComponents.forEach(component => { %>
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" 
                                               id="selected_<%= component.name %>"
                                               name="selectedIncomes[]" 
                                               value="<%= component.name %>"
                                               <%= rfiConfig.selectedIncomes && 
                                                   rfiConfig.selectedIncomes.some(income => 
                                                       income.name === component.name || 
                                                       (income.componentId && income.componentId.name === component.name)
                                                   ) ? 'checked' : '' %> />
                                        <label for="selected_<%= component.name %>">
                                            <i class="ph ph-check"></i>
                                            <%= component.name %>
                                            <% if (component.amount) { %>
                                                <span class="amount">
                                                    (R<%= component.amount.toLocaleString('en-ZA', { minimumFractionDigits: 2 }) %>)
                                                </span>
                                            <% } %>
                                        </label>
                                    </div>
                                <% }); %>
                            </div>
                            
                            <!-- Add a helper text -->
                            <div class="helper-text mt-3">
                                <small class="text-muted">
                                    <i class="ph ph-info"></i>
                                    Selected components will be included at 100% of their value in RFI calculations
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- Percentage per Income Card -->
                    <div id="percentageCard" class="edit-card" style="display: <%= rfiConfig.option === 'percentagePerIncome' ? 'block' : 'none' %>">
                        <div class="card-header">
                            <h2><i class="ph ph-percent"></i> Percentage per Income</h2>
                            <% if (payComponents.length === 0) { %>
                                <div class="no-components-warning">
                                    <i class="ph ph-warning"></i>
                                    <span>No pay components found. Please set up pay components first.</span>
                                </div>
                            <% } %>
                        </div>
                        <div class="card-content">
                            <div class="percentage-grid">
                                <% payComponents.forEach(component => { %>
                                    <div class="percentage-row">
                                        <div class="component-details">
                                            <span class="component-name"><%= component.name %></span>
                                            <span class="component-amount">R<%= component.amount.toFixed(2) %></span>
                                        </div>
                                        <div class="percentage-input-wrapper">
                                            <input type="number" 
                                                   name="percentageValues[<%= component.name %>]" 
                                                   class="percentage-input"
                                                   min="0"
                                                   max="100"
                                                   step="0.01"
                                                   value="<%= (rfiConfig.percentageIncomes && rfiConfig.percentageIncomes[component.name]) || '' %>"
                                                   placeholder="Enter %" />
                                            <i class="ph ph-percent"></i>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" 
                                onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                            <i class="ph ph-x"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ph ph-check"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="/js/defineRFI.js"></script>
    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
    // Dropdown toggle function
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdown = event.target.closest('.dropdown');
      const dropdownContent = dropdown.querySelector('.dropdown-content');

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-content').forEach(content => {
        if (content !== dropdownContent) {
          content.classList.remove('show');
        }
      });

      // Toggle current dropdown
      dropdownContent.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(content => {
          content.classList.remove('show');
        });
      }
    });

    // Delete employee function
    function handleDeleteEmployee(employeeId) {
      if (typeof window.confirmDeleteEmployee === 'function') {
        window.confirmDeleteEmployee(employeeId);
      } else {
        console.error('confirmDeleteEmployee function not available. Please refresh the page.');
        alert('Delete function not available. Please refresh the page and try again.');
      }
    }

    // Define missing functions to prevent errors
    window.updatePageIndicator = window.updatePageIndicator || function() {
      // No-op function for pages that don't have page indicators
    };
    </script>
</body>
</html>
