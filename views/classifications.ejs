<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <title>Employee Classifications - <%= employee.firstName %> <%= employee.lastName %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css" />
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/edit-employee-details.css" />
    <link rel="stylesheet" href="/css/employeeManagement.css" />
    <link rel="stylesheet" href="/css/header.css" />
    <link rel="stylesheet" href="/css/sidebar.css" />

    <!-- Add CSRF token -->
    <meta name="csrf-token" content="<%= csrfToken %>" />

    <style>
      /* Dropdown Styles for Edit Info */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #6b7280;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid #f3f4f6;
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: #f3f4f6;
        color: #111827;
      }

      .dropdown-content a.active {
        background: #818cf8;
        color: white;
      }

      .dropdown-content a i {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .dropdown-content a.active i {
        color: white;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar') %>
        <div class="content-wrapper">
            <%- include('partials/header') %>

            <main class="main-container" style="padding-top: 12rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications" class="active">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <form id="classificationsForm" method="post"
                      action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>" />
                    
                    <!-- Employment Type Card -->
                    <div class="edit-card">
                        <div class="card-header">
                            <h2><i class="ph ph-briefcase"></i> Employment Type</h2>
                        </div>
                        <div class="card-content">
                     <!-- Working Hours (wrap in a div for toggling) -->
                        <div id="workingHoursGroup" class="form-group">
                            <label for="workingHours">Working Hours</label>
                            <div class="select-wrapper">
                                <select id="workingHours" name="workingHours" required>
                                    <option value="">Select working hours</option>
                                    <option value="Full Time" <%= employee.workingHours === 'Full Time' ? 'selected' : '' %>>
                                        Full Time
                                    </option>
                                    <option value="Less Than 22 Hours a Week" <%= employee.workingHours === 'Less Than 22 Hours a Week' ? 'selected' : '' %>>
                                        Less Than 22 Hours a Week
                                    </option>
                                </select>
                                <i class="ph ph-caret-down"></i>
                            </div>
                        </div>

                            <!-- Director Checkbox -->
                            <div class="form-group">
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" id="directorCheckbox" name="isDirector" 
                                           <%= employee.isDirector === true ? 'checked' : '' %>>
                                    <label for="directorCheckbox">
                                        <i class="ph ph-user-circle-gear"></i>
                                        Director
                                    </label>
                                </div>
                            </div>

                            <!-- Director Type (shows when Director is checked) -->
                            <div class="form-group" id="directorTypeGroup" style="display:none">
                                <label for="typeOfDirector">Type of Director</label>
                                <div class="select-wrapper">
                                    <select id="typeOfDirector" name="typeOfDirector">
                                        <option value="">Select director type</option>
                                        <option value="Executive Director/ Member of a CC" 
                                                <%= employee.typeOfDirector === 'Executive Director/ Member of a CC' ? 'selected' : '' %>>
                                            Executive Director/ Member of a CC
                                        </option>
                                        <option value="RSA Resident Non-Executive Director"
                                                <%= employee.typeOfDirector === 'RSA Resident Non-Executive Director' ? 'selected' : '' %>>
                                            RSA Resident Non-Executive Director
                                        </option>
                                        <option value="Non Resident Non-Executive Director"
                                                <%= employee.typeOfDirector === 'Non Resident Non-Executive Director' ? 'selected' : '' %>>
                                            Non Resident Non-Executive Director
                                        </option>
                                    </select>
                                    <i class="ph ph-caret-down"></i>
                                </div>
                            </div>

                            <!-- Independent Contractor Checkbox -->
                            <div class="form-group">
                                <div class="checkbox-wrapper">
                                    <input type="checkbox" id="contractorCheckbox" name="isContractor" 
                                           <%= employee.isContractor === true ? 'checked' : '' %>>
                                    <label for="contractorCheckbox">
                                        <i class="ph ph-file-contract"></i>
                                        Independent Contractor
                                    </label>
                                </div>
                            </div>

                            <!-- UIF Exempt Section -->
                            <div id="uifExemptContainer">
                                <div class="form-group">
                                    <div class="checkbox-wrapper">
                                        <input type="checkbox" id="uifExemptCheckbox" name="isUifExempt" 
                                               <%= employee.isUifExempt === true ? 'checked' : '' %>>
                                        <label for="uifExemptCheckbox">
                                            <i class="ph ph-shield-check"></i>
                                            UIF Exempt
                                        </label>
                                    </div>
                                </div>

                                <!-- UIF Exempt Reason (shows when UIF Exempt is checked) -->
                                <div class="form-group" id="uifExemptReasonGroup" style="display:none">
                                    <label for="uifExemptReason">Reason for UIF Exemption</label>
                                    <div class="select-wrapper">
                                        <select id="uifExemptReason" name="uifExemptReason">
                                            <option value="">Select reason</option>
                                            <option value="Works less than 24 hours per month"
                                                    <%= employee.uifExemptReason === 'Works less than 24 hours per month' ? 'selected' : '' %>>
                                                Works less than 24 hours per month
                                            </option>
                                            <option value="Public Servant"
                                                    <%= employee.uifExemptReason === 'Public Servant' ? 'selected' : '' %>>
                                                Public Servant
                                            </option>
                                            <option value="Earns Commission Only"
                                                    <%= employee.uifExemptReason === 'Earns Commission Only' ? 'selected' : '' %>>
                                                Earns Commission Only
                                            </option>
                                            <option value="Learner in terms of the SDL act"
                                                    <%= employee.uifExemptReason === 'Learner in terms of the SDL act' ? 'selected' : '' %>>
                                                Learner in terms of the SDL act
                                            </option>
                                            <option value="Foreigner - Leaving South Africa at end of service"
                                                    <%= employee.uifExemptReason === 'Foreigner - Leaving South Africa at end of service' ? 'selected' : '' %>>
                                                Foreigner - Leaving South Africa at end of service
                                            </option>
                                        </select>
                                        <i class="ph ph-caret-down"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" 
                                onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                            <i class="ph ph-x"></i>
                            Cancel
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="ph ph-check"></i>
                            Save Changes
                        </button>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="/js/classifications.js"></script>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
    // Dropdown toggle function
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdown = event.target.closest('.dropdown');
      const dropdownContent = dropdown.querySelector('.dropdown-content');

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-content').forEach(content => {
        if (content !== dropdownContent) {
          content.classList.remove('show');
        }
      });

      // Toggle current dropdown
      dropdownContent.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(content => {
          content.classList.remove('show');
        });
      }
    });

    // Delete employee function
    function handleDeleteEmployee(employeeId) {
      if (typeof window.confirmDeleteEmployee === 'function') {
        window.confirmDeleteEmployee(employeeId);
      } else {
        console.error('confirmDeleteEmployee function not available. Please refresh the page.');
        alert('Delete function not available. Please refresh the page and try again.');
      }
    }

    // Define missing functions to prevent errors
    window.updatePageIndicator = window.updatePageIndicator || function() {
      // No-op function for pages that don't have page indicators
    };
    </script>
</body>
</html>
