<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ETI Configuration | <%= company.name %></title>

    <!-- Modern Font -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <script src="https://unpkg.com/@phosphor-icons/web"></script>

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
    <link rel="stylesheet" href="/css/employeeManagement.css">
    <link rel="stylesheet" href="/css/onboarding.css" />
    <link rel="stylesheet" href="/css/header.css">
    <link rel="stylesheet" href="/css/sidebar.css">
    <link rel="stylesheet" href="/css/eti.css">

    <!-- Flatpickr CSS and JS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>

    <style>
      /* Dropdown Styles for Edit Info */
      .dropdown {
        position: relative;
        display: inline-block;
      }

      .dropdown-content {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #ffffff;
        border: 1px solid #e5e7eb;
        border-radius: 2px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        z-index: 1000;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
      }

      .dropdown-content a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        color: #6b7280;
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 400;
        transition: background-color 0.1s ease;
        border-bottom: 1px solid #f3f4f6;
      }

      .dropdown-content a:last-child {
        border-bottom: none;
      }

      .dropdown-content a:hover {
        background: #f3f4f6;
        color: #111827;
      }

      .dropdown-content a.active {
        background: #818cf8;
        color: white;
      }

      .dropdown-content a i {
        color: #6b7280;
        font-size: 0.875rem;
      }

      .dropdown-content a.active i {
        color: white;
      }

      .dropdown-content.show {
        display: block;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .dropdown-content {
          position: relative;
          display: block;
          margin-top: 0.5rem;
          box-shadow: none;
          border: 1px solid #e5e7eb;
          border-radius: 2px;
          width: 100%;
          left: 0;
          right: 0;
        }

        .dropdown-content a {
          padding: 1rem;
          font-size: 0.875rem;
          min-height: 44px;
          display: flex;
          align-items: center;
        }
      }
    </style>
</head>
<body>
    <div class="layout-wrapper">
        <%- include('partials/sidebar', { user: user, company: company }) %>
        <div class="content-wrapper">
            <%- include('partials/header', { user: user }) %>

            <main class="main-container" style="padding-top: 12rem;">
                <!-- Professional Action Navigation -->
                <div class="action-tabs">
                    <!-- Payroll Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                        <i class="ph ph-calculator"></i>
                        Payroll
                    </button>

                    <!-- Edit Info Dropdown -->
                    <div class="dropdown">
                        <button class="tab-button active" onclick="toggleDropdown(event)">
                            <i class="ph ph-pencil-simple"></i>
                            Edit Info
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div id="dropdownContent" class="dropdown-content">
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/edit">
                                <i class="ph ph-user"></i>
                                Basic Info
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/classifications">
                                <i class="ph ph-tag"></i>
                                Classifications
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/defineRFI">
                                <i class="ph ph-file-text"></i>
                                Define RFI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/regularHours">
                                <i class="ph ph-clock"></i>
                                Regular Hours
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti" class="active">
                                <i class="ph ph-chart-line"></i>
                                ETI
                            </a>
                            <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/skillsEquity">
                                <i class="ph ph-star"></i>
                                Skills Equity
                            </a>
                        </div>
                    </div>

                    <!-- Leave Tab -->
                    <button class="tab-button" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeManagement/employee/<%= employee._id %>/leave'">
                        <i class="ph ph-calendar-blank"></i>
                        Leave
                    </button>

                    <!-- End Service Tab -->
                    <a href="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/end-service"
                       class="tab-button warning"
                       role="button">
                        <i class="ph ph-sign-out"></i>
                        <%= (employee.status === 'Inactive' || employee.status === 'Serving Notice') ? 'Manage End of Service' : 'End Service' %>
                    </a>

                    <!-- Delete Employee Tab -->
                    <button class="tab-button danger" onclick="handleDeleteEmployee('<%= employee._id %>')">
                        <i class="ph ph-trash"></i>
                        Delete Employee
                    </button>
                </div>

                <!-- ETI Form Card -->
                <form class="action-card" action="/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>/eti" method="post">
                    <!-- Add CSRF token hidden input -->
                    <input type="hidden" name="_csrf" value="<%= csrfToken %>">
                    
                    <!-- ETI Configuration Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-file-text"></i>
                            ETI Configuration
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="form-group">
                            <label for="status">
                                <i class="ph ph-toggle-left"></i>
                                Status
                            </label>
                            <select id="status" name="status" class="filter-select" required>
                                <option value="Qualified - Not Claiming" <%= employee.etiStatus === 'Qualified - Not Claiming' ? 'selected' : '' %>>
                                    Qualified - Not Claiming
                                </option>
                                <option value="Qualified - Claiming" <%= employee.etiStatus === 'Qualified - Claiming' ? 'selected' : '' %>>
                                    Qualified - Claiming
                                </option>
                                <option value="Disqualified" <%= employee.etiStatus === 'Disqualified' ? 'selected' : '' %>>
                                    Disqualified
                                </option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="effectiveFrom">
                                <i class="ph ph-calendar"></i>
                                Effective from
                            </label>
                            <input type="text" 
                                   id="effectiveFrom" 
                                   name="effectiveFrom" 
                                   class="filter-select flatpickr-input"
                                   value="<%= employee.etiEffectiveFrom ? new Date(employee.etiEffectiveFrom).toISOString().split('T')[0] : '' %>"
                                   required>
                        </div>
                    </div>

                    <!-- History Section -->
                    <div class="card-header">
                        <h3>
                            <i class="ph ph-clock-counter-clockwise"></i>
                            ETI History
                        </h3>
                    </div>
                    
                    <div class="card-content">
                        <div class="table-container">
                            <table class="modern-table">
                                <thead>
                                    <tr>
                                        <th>Effective Date</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <% if (employee.etiHistory && employee.etiHistory.length > 0) { %>
                                        <% employee.etiHistory.forEach(function(entry) { %>
                                            <tr>
                                                <td><%= new Date(entry.effectiveDate).toLocaleDateString() %></td>
                                                <td>
                                                    <span class="status-badge <%= entry.status.toLowerCase().replace(/\s+/g, '-') %>">
                                                        <%= entry.status %>
                                                    </span>
                                                </td>
                                            </tr>
                                        <% }); %>
                                    <% } else { %>
                                        <tr>
                                            <td colspan="2" style="text-align: center; color: #64748b;">
                                                No history available
                                            </td>
                                        </tr>
                                    <% } %>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Form Actions -->
                    <div class="card-content" style="border-top: 1px solid #e2e8f0;">
                        <div class="button-group">
                            <button type="button" class="btn btn-secondary" onclick="window.location.href='/clients/<%= company.companyCode %>/employeeProfile/<%= employee._id %>'">
                                <i class="ph ph-x"></i>
                                Cancel
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="ph ph-check"></i>
                                Save Changes
                            </button>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Flatpickr with custom configuration
        const datePicker = flatpickr("#effectiveFrom", {
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "F Y", // Shows only Month Year in the input
            defaultDate: "<%= employee.etiEffectiveFrom ? new Date(employee.etiEffectiveFrom).toISOString().split('T')[0] : new Date().toISOString().split('T')[0] %>",
            
            // Force selection of first day of month
            onOpen: function(selectedDates, dateStr, instance) {
                if (selectedDates[0]) {
                    const date = new Date(selectedDates[0]);
                    date.setDate(1);
                    instance.setDate(date);
                }
            },
            
            onChange: function(selectedDates, dateStr, instance) {
                if (selectedDates[0]) {
                    // Always set to first day of selected month
                    const date = new Date(selectedDates[0]);
                    date.setDate(1);
                    date.setHours(0, 0, 0, 0);
                    instance.setDate(date);
                }
            },

            // Only allow selecting first day of month
            enable: [
                function(date) {
                    return date.getDate() === 1;
                }
            ],

            // Ensure the picker opens to the first of the month
            onReady: function(selectedDates, dateStr, instance) {
                const currentDate = selectedDates[0] || new Date();
                const firstOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
                instance.setDate(firstOfMonth, false);
            }
        });

        // Additional check before form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            const dateInput = document.getElementById('effectiveFrom');
            const selectedDate = new Date(dateInput.value);
            selectedDate.setDate(1);
            selectedDate.setHours(0, 0, 0, 0);
            dateInput.value = selectedDate.toISOString().split('T')[0];
        });
    });
    </script>

    <!-- Employee Actions Script for consistent modal functionality -->
    <script src="/js/employeeActions.js"></script>

    <script>
    // Dropdown toggle function
    function toggleDropdown(event) {
      event.preventDefault();
      event.stopPropagation();

      const dropdown = event.target.closest('.dropdown');
      const dropdownContent = dropdown.querySelector('.dropdown-content');

      // Close all other dropdowns
      document.querySelectorAll('.dropdown-content').forEach(content => {
        if (content !== dropdownContent) {
          content.classList.remove('show');
        }
      });

      // Toggle current dropdown
      dropdownContent.classList.toggle('show');
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!event.target.closest('.dropdown')) {
        document.querySelectorAll('.dropdown-content').forEach(content => {
          content.classList.remove('show');
        });
      }
    });

    // Delete employee function
    function handleDeleteEmployee(employeeId) {
      if (typeof window.confirmDeleteEmployee === 'function') {
        window.confirmDeleteEmployee(employeeId);
      } else {
        console.error('confirmDeleteEmployee function not available. Please refresh the page.');
        alert('Delete function not available. Please refresh the page and try again.');
      }
    }

    // Define missing functions to prevent errors
    window.updatePageIndicator = window.updatePageIndicator || function() {
      // No-op function for pages that don't have page indicators
    };
    </script>
</body>
</html>